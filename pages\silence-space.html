<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Silence Space - Rewire</title>
    <link rel="stylesheet" href="../css/main.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <div class="silence-container">
        <header class="silence-header">
            <button class="back-btn" onclick="goBack()">
                <span class="back-icon">←</span>
                <span>Back</span>
            </button>
            <h1 class="silence-title">
                <span class="silence-icon">🎧</span>
                Silence Space
            </h1>
            <div class="session-time">
                <span class="time-number" id="total-time">0</span>
                <span class="time-label">Minutes Today</span>
            </div>
        </header>

        <main class="silence-main">
            <!-- Ambient Environment Selector -->
            <section class="environment-selector">
                <h2>Choose Your Environment</h2>
                <div class="environments-grid">
                    
                    <div class="environment-card active" data-environment="rain" onclick="selectEnvironment('rain')">
                        <div class="env-icon">🌧️</div>
                        <h3>Gentle Rain</h3>
                        <p>Soft rainfall for deep relaxation</p>
                    </div>
                    
                    <div class="environment-card" data-environment="forest" onclick="selectEnvironment('forest')">
                        <div class="env-icon">🌲</div>
                        <h3>Forest Sounds</h3>
                        <p>Birds chirping in a peaceful forest</p>
                    </div>
                    
                    <div class="environment-card" data-environment="ocean" onclick="selectEnvironment('ocean')">
                        <div class="env-icon">🌊</div>
                        <h3>Ocean Waves</h3>
                        <p>Rhythmic waves on a calm beach</p>
                    </div>
                    
                    <div class="environment-card" data-environment="fire" onclick="selectEnvironment('fire')">
                        <div class="env-icon">🔥</div>
                        <h3>Crackling Fire</h3>
                        <p>Warm fireplace ambiance</p>
                    </div>
                    
                    <div class="environment-card" data-environment="wind" onclick="selectEnvironment('wind')">
                        <div class="env-icon">💨</div>
                        <h3>Desert Wind</h3>
                        <p>Gentle wind through open spaces</p>
                    </div>
                    
                    <div class="environment-card" data-environment="silence" onclick="selectEnvironment('silence')">
                        <div class="env-icon">🤫</div>
                        <h3>Pure Silence</h3>
                        <p>Complete quiet for deep focus</p>
                    </div>
                    
                </div>
            </section>

            <!-- Session Controls -->
            <section class="session-controls">
                <div class="timer-setup">
                    <h3>Session Duration</h3>
                    <div class="duration-buttons">
                        <button class="duration-btn active" data-duration="5" onclick="setDuration(5)">5 min</button>
                        <button class="duration-btn" data-duration="10" onclick="setDuration(10)">10 min</button>
                        <button class="duration-btn" data-duration="15" onclick="setDuration(15)">15 min</button>
                        <button class="duration-btn" data-duration="20" onclick="setDuration(20)">20 min</button>
                        <button class="duration-btn" data-duration="30" onclick="setDuration(30)">30 min</button>
                        <button class="duration-btn custom" onclick="setCustomDuration()">Custom</button>
                    </div>
                </div>

                <div class="volume-control">
                    <h3>Volume</h3>
                    <div class="volume-slider">
                        <span class="volume-icon">🔈</span>
                        <input type="range" id="volume-slider" min="0" max="100" value="50" onchange="adjustVolume(this.value)">
                        <span class="volume-icon">🔊</span>
                    </div>
                </div>

                <div class="session-actions">
                    <button class="btn btn-primary session-btn" id="start-btn" onclick="startSession()">
                        <span class="btn-icon">▶️</span>
                        Start Session
                    </button>
                    <button class="btn btn-secondary session-btn" id="pause-btn" onclick="pauseSession()" style="display: none;">
                        <span class="btn-icon">⏸️</span>
                        Pause
                    </button>
                    <button class="btn btn-secondary session-btn" id="stop-btn" onclick="stopSession()" style="display: none;">
                        <span class="btn-icon">⏹️</span>
                        Stop
                    </button>
                </div>
            </section>

            <!-- Active Session Display -->
            <section class="active-session" id="active-session" style="display: none;">
                <div class="session-display">
                    <div class="current-environment" id="current-environment">
                        <div class="env-visual">🌧️</div>
                        <h3 id="env-name">Gentle Rain</h3>
                    </div>
                    
                    <div class="session-timer">
                        <div class="timer-circle">
                            <svg class="timer-svg" viewBox="0 0 100 100">
                                <circle class="timer-bg" cx="50" cy="50" r="45"></circle>
                                <circle class="timer-progress" id="timer-progress" cx="50" cy="50" r="45"></circle>
                            </svg>
                            <div class="timer-text">
                                <span class="timer-minutes" id="timer-minutes">05</span>
                                <span class="timer-separator">:</span>
                                <span class="timer-seconds" id="timer-seconds">00</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="session-guidance" id="session-guidance">
                        <p>Close your eyes and focus on your breathing. Let the sounds wash over you.</p>
                    </div>
                </div>
            </section>

            <!-- Session History -->
            <section class="session-history">
                <h2>Your Silence Journey</h2>
                <div class="history-stats">
                    <div class="stat-card">
                        <div class="stat-icon">⏱️</div>
                        <div class="stat-content">
                            <h3>Total Time</h3>
                            <span class="stat-value" id="total-minutes">0 minutes</span>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon">📅</div>
                        <div class="stat-content">
                            <h3>Sessions</h3>
                            <span class="stat-value" id="total-sessions">0 sessions</span>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon">🔥</div>
                        <div class="stat-content">
                            <h3>Streak</h3>
                            <span class="stat-value" id="session-streak">0 days</span>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon">❤️</div>
                        <div class="stat-content">
                            <h3>Favorite</h3>
                            <span class="stat-value" id="favorite-sound">Rain</span>
                        </div>
                    </div>
                </div>

                <div class="recent-sessions">
                    <h3>Recent Sessions</h3>
                    <div class="sessions-list" id="sessions-list">
                        <div class="empty-state">
                            <p>No sessions yet. Start your first silence session above!</p>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Guided Meditations -->
            <section class="guided-meditations">
                <h2>Guided Sessions</h2>
                <div class="meditations-grid">
                    
                    <div class="meditation-card" onclick="startGuidedMeditation('breathing')">
                        <div class="meditation-icon">🫁</div>
                        <h3>Breathing Focus</h3>
                        <p>5-minute guided breathing exercise</p>
                        <div class="meditation-duration">5 min</div>
                    </div>
                    
                    <div class="meditation-card" onclick="startGuidedMeditation('body-scan')">
                        <div class="meditation-icon">🧘</div>
                        <h3>Body Scan</h3>
                        <p>Progressive relaxation technique</p>
                        <div class="meditation-duration">10 min</div>
                    </div>
                    
                    <div class="meditation-card" onclick="startGuidedMeditation('mindfulness')">
                        <div class="meditation-icon">🧠</div>
                        <h3>Mindfulness</h3>
                        <p>Present moment awareness practice</p>
                        <div class="meditation-duration">15 min</div>
                    </div>
                    
                    <div class="meditation-card" onclick="startGuidedMeditation('strength')">
                        <div class="meditation-icon">💪</div>
                        <h3>Mental Strength</h3>
                        <p>Build resilience and inner power</p>
                        <div class="meditation-duration">20 min</div>
                    </div>
                    
                </div>
            </section>
        </main>

        <!-- Completion Modal -->
        <div class="completion-modal" id="completion-modal" style="display: none;">
            <div class="modal-overlay"></div>
            <div class="modal-content">
                <div class="modal-header">
                    <h3>🎉 Session Complete!</h3>
                </div>
                <div class="modal-body">
                    <div class="completion-stats">
                        <div class="completion-stat">
                            <span class="stat-label">Duration:</span>
                            <span class="stat-value" id="completed-duration">5 minutes</span>
                        </div>
                        <div class="completion-stat">
                            <span class="stat-label">Environment:</span>
                            <span class="stat-value" id="completed-environment">Gentle Rain</span>
                        </div>
                    </div>
                    <div class="completion-message">
                        <p>Well done! You've taken another step toward mental clarity and inner peace.</p>
                    </div>
                    <div class="mood-check">
                        <h4>How do you feel now?</h4>
                        <div class="mood-options">
                            <button class="mood-btn" onclick="setPostSessionMood('calm')">😌 Calm</button>
                            <button class="mood-btn" onclick="setPostSessionMood('focused')">🎯 Focused</button>
                            <button class="mood-btn" onclick="setPostSessionMood('peaceful')">☮️ Peaceful</button>
                            <button class="mood-btn" onclick="setPostSessionMood('energized')">⚡ Energized</button>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-primary" onclick="closeCompletionModal()">Continue</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Audio Elements -->
    <audio id="rain-audio" loop>
        <source src="../assets/audio/rain.mp3" type="audio/mpeg">
    </audio>
    <audio id="forest-audio" loop>
        <source src="../assets/audio/forest.mp3" type="audio/mpeg">
    </audio>
    <audio id="ocean-audio" loop>
        <source src="../assets/audio/ocean.mp3" type="audio/mpeg">
    </audio>
    <audio id="fire-audio" loop>
        <source src="../assets/audio/fire.mp3" type="audio/mpeg">
    </audio>
    <audio id="wind-audio" loop>
        <source src="../assets/audio/wind.mp3" type="audio/mpeg">
    </audio>

    <script src="../js/storage.js"></script>
    <script src="../js/silence-space.js"></script>
    <script>
        function goBack() {
            window.history.back();
        }

        function selectEnvironment(env) {
            if (window.silenceSpace) {
                window.silenceSpace.selectEnvironment(env);
            }
        }

        function setDuration(minutes) {
            if (window.silenceSpace) {
                window.silenceSpace.setDuration(minutes);
            }
        }

        function setCustomDuration() {
            const duration = prompt('Enter duration in minutes (1-120):');
            if (duration && !isNaN(duration) && duration > 0 && duration <= 120) {
                setDuration(parseInt(duration));
            }
        }

        function adjustVolume(value) {
            if (window.silenceSpace) {
                window.silenceSpace.adjustVolume(value);
            }
        }

        function startSession() {
            if (window.silenceSpace) {
                window.silenceSpace.startSession();
            }
        }

        function pauseSession() {
            if (window.silenceSpace) {
                window.silenceSpace.pauseSession();
            }
        }

        function stopSession() {
            if (window.silenceSpace) {
                window.silenceSpace.stopSession();
            }
        }

        function startGuidedMeditation(type) {
            if (window.silenceSpace) {
                window.silenceSpace.startGuidedMeditation(type);
            }
        }

        function setPostSessionMood(mood) {
            if (window.silenceSpace) {
                window.silenceSpace.setPostSessionMood(mood);
            }
        }

        function closeCompletionModal() {
            document.getElementById('completion-modal').style.display = 'none';
        }

        document.addEventListener('DOMContentLoaded', () => {
            if (window.SilenceSpace) {
                window.silenceSpace = new SilenceSpace();
            }
        });
    </script>

    <style>
        .silence-container {
            min-height: 100vh;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 100%);
        }

        .silence-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: var(--spacing-lg) var(--spacing-xl);
            background: rgba(26, 26, 46, 0.8);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid var(--bg-tertiary);
        }

        .silence-title {
            display: flex;
            align-items: center;
            gap: var(--spacing-md);
            font-size: 1.75rem;
            font-weight: 600;
            color: var(--text-primary);
        }

        .session-time {
            text-align: center;
        }

        .time-number {
            display: block;
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--accent-primary);
        }

        .time-label {
            font-size: 0.875rem;
            color: var(--text-secondary);
        }

        .silence-main {
            max-width: 1200px;
            margin: 0 auto;
            padding: var(--spacing-xl);
        }

        .silence-main section {
            margin-bottom: var(--spacing-2xl);
        }

        .environments-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: var(--spacing-lg);
        }

        .environment-card {
            background: rgba(26, 26, 46, 0.6);
            padding: var(--spacing-xl);
            border-radius: var(--radius-lg);
            border: 2px solid transparent;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
            backdrop-filter: blur(10px);
        }

        .environment-card:hover {
            border-color: var(--accent-primary);
            transform: translateY(-2px);
        }

        .environment-card.active {
            border-color: var(--accent-primary);
            background: rgba(99, 102, 241, 0.2);
        }

        .env-icon {
            font-size: 3rem;
            margin-bottom: var(--spacing-md);
        }

        .environment-card h3 {
            color: var(--text-primary);
            margin-bottom: var(--spacing-sm);
        }

        .environment-card p {
            color: var(--text-secondary);
            font-size: 0.875rem;
        }

        .session-controls {
            background: rgba(26, 26, 46, 0.6);
            padding: var(--spacing-2xl);
            border-radius: var(--radius-lg);
            backdrop-filter: blur(10px);
        }

        .timer-setup {
            margin-bottom: var(--spacing-xl);
        }

        .duration-buttons {
            display: flex;
            gap: var(--spacing-md);
            flex-wrap: wrap;
            margin-top: var(--spacing-md);
        }

        .duration-btn {
            padding: var(--spacing-sm) var(--spacing-lg);
            background: var(--bg-tertiary);
            border: 2px solid transparent;
            border-radius: var(--radius-md);
            color: var(--text-secondary);
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .duration-btn:hover,
        .duration-btn.active {
            border-color: var(--accent-primary);
            color: var(--text-primary);
            background: rgba(99, 102, 241, 0.2);
        }

        .volume-control {
            margin-bottom: var(--spacing-xl);
        }

        .volume-slider {
            display: flex;
            align-items: center;
            gap: var(--spacing-md);
            margin-top: var(--spacing-md);
        }

        .volume-slider input[type="range"] {
            flex: 1;
            height: 6px;
            background: var(--bg-tertiary);
            border-radius: 3px;
            outline: none;
        }

        .volume-slider input[type="range"]::-webkit-slider-thumb {
            appearance: none;
            width: 20px;
            height: 20px;
            background: var(--accent-primary);
            border-radius: 50%;
            cursor: pointer;
        }

        .session-actions {
            display: flex;
            gap: var(--spacing-md);
            justify-content: center;
        }

        .session-btn {
            min-width: 140px;
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
            justify-content: center;
        }

        .active-session {
            background: rgba(26, 26, 46, 0.8);
            padding: var(--spacing-2xl);
            border-radius: var(--radius-lg);
            text-align: center;
            backdrop-filter: blur(10px);
        }

        .current-environment {
            margin-bottom: var(--spacing-xl);
        }

        .env-visual {
            font-size: 4rem;
            margin-bottom: var(--spacing-md);
        }

        .session-timer {
            margin-bottom: var(--spacing-xl);
        }

        .timer-circle {
            position: relative;
            width: 200px;
            height: 200px;
            margin: 0 auto;
        }

        .timer-svg {
            width: 100%;
            height: 100%;
            transform: rotate(-90deg);
        }

        .timer-bg {
            fill: none;
            stroke: var(--bg-tertiary);
            stroke-width: 4;
        }

        .timer-progress {
            fill: none;
            stroke: var(--accent-primary);
            stroke-width: 4;
            stroke-linecap: round;
            stroke-dasharray: 283;
            stroke-dashoffset: 283;
            transition: stroke-dashoffset 1s linear;
        }

        .timer-text {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 2rem;
            font-weight: 700;
            color: var(--text-primary);
            font-family: 'Courier New', monospace;
        }

        .session-guidance {
            color: var(--text-secondary);
            font-style: italic;
            max-width: 400px;
            margin: 0 auto;
        }

        .history-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: var(--spacing-lg);
            margin-bottom: var(--spacing-xl);
        }

        .stat-card {
            background: rgba(26, 26, 46, 0.6);
            padding: var(--spacing-xl);
            border-radius: var(--radius-lg);
            display: flex;
            align-items: center;
            gap: var(--spacing-lg);
            backdrop-filter: blur(10px);
        }

        .stat-icon {
            font-size: 2rem;
        }

        .stat-content h3 {
            color: var(--text-secondary);
            font-size: 0.875rem;
            margin-bottom: var(--spacing-xs);
        }

        .stat-value {
            color: var(--text-primary);
            font-size: 1.25rem;
            font-weight: 600;
        }

        .sessions-list {
            background: rgba(26, 26, 46, 0.6);
            padding: var(--spacing-lg);
            border-radius: var(--radius-lg);
            backdrop-filter: blur(10px);
        }

        .meditations-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: var(--spacing-lg);
        }

        .meditation-card {
            background: rgba(26, 26, 46, 0.6);
            padding: var(--spacing-xl);
            border-radius: var(--radius-lg);
            border: 2px solid transparent;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
            backdrop-filter: blur(10px);
        }

        .meditation-card:hover {
            border-color: var(--accent-primary);
            transform: translateY(-2px);
        }

        .meditation-icon {
            font-size: 2.5rem;
            margin-bottom: var(--spacing-md);
        }

        .meditation-duration {
            background: var(--accent-primary);
            color: white;
            padding: var(--spacing-xs) var(--spacing-sm);
            border-radius: var(--radius-sm);
            font-size: 0.875rem;
            font-weight: 500;
            display: inline-block;
            margin-top: var(--spacing-md);
        }

        /* Modal Styles */
        .completion-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            z-index: 1000;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: var(--spacing-lg);
        }

        .modal-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.8);
        }

        .modal-content {
            background: var(--bg-secondary);
            border-radius: var(--radius-lg);
            max-width: 500px;
            width: 100%;
            position: relative;
            z-index: 1001;
        }

        .modal-header {
            padding: var(--spacing-xl);
            border-bottom: 1px solid var(--bg-tertiary);
            text-align: center;
        }

        .modal-body {
            padding: var(--spacing-xl);
        }

        .completion-stats {
            display: flex;
            justify-content: space-around;
            margin-bottom: var(--spacing-lg);
        }

        .completion-stat {
            text-align: center;
        }

        .completion-stat .stat-label {
            color: var(--text-secondary);
            font-size: 0.875rem;
            display: block;
            margin-bottom: var(--spacing-xs);
        }

        .completion-stat .stat-value {
            color: var(--text-primary);
            font-weight: 600;
        }

        .mood-options {
            display: flex;
            gap: var(--spacing-sm);
            justify-content: center;
            flex-wrap: wrap;
        }

        .mood-btn {
            background: var(--bg-tertiary);
            border: 1px solid var(--bg-tertiary);
            color: var(--text-secondary);
            padding: var(--spacing-sm) var(--spacing-md);
            border-radius: var(--radius-md);
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .mood-btn:hover {
            border-color: var(--accent-primary);
            color: var(--text-primary);
        }

        .modal-footer {
            padding: var(--spacing-xl);
            border-top: 1px solid var(--bg-tertiary);
            text-align: center;
        }

        @media (max-width: 768px) {
            .silence-header {
                flex-direction: column;
                gap: var(--spacing-md);
            }

            .environments-grid {
                grid-template-columns: repeat(2, 1fr);
            }

            .duration-buttons {
                justify-content: center;
            }

            .session-actions {
                flex-direction: column;
                align-items: center;
            }

            .timer-circle {
                width: 150px;
                height: 150px;
            }

            .timer-text {
                font-size: 1.5rem;
            }

            .history-stats {
                grid-template-columns: repeat(2, 1fr);
            }

            .meditations-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</body>
</html>
