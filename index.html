<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Rewire - Transform Your Mind</title>
    <link rel="stylesheet" href="css/main.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <div class="container">
        <!-- Hero Section -->
        <header class="hero">
            <div class="hero-content">
                <h1 class="hero-title">
                    <span class="gradient-text">Rewire</span>
                    <span class="subtitle">Your Mind</span>
                </h1>
                <p class="hero-description">
                    Transform broken, distracted minds into focused, emotionally intelligent, and mentally strong individuals.
                    <br>
                    <span class="highlight">Anonymous. Personal. Powerful.</span>
                </p>
                <div class="cta-buttons">
                    <button class="btn btn-primary" onclick="enterPlatform()">
                        Begin Transformation
                    </button>
                    <button class="btn btn-secondary" onclick="showDemo()">
                        See How It Works
                    </button>
                </div>
            </div>
            <div class="hero-visual">
                <div class="mind-visualization">
                    <div class="neural-network"></div>
                </div>
            </div>
        </header>

        <!-- Features Grid -->
        <section class="features" id="features">
            <h2 class="section-title">Your Mental Transformation Toolkit</h2>
            <div class="features-grid">
                
                <div class="feature-card" data-feature="therapy">
                    <div class="feature-icon">🧠</div>
                    <h3>AI Therapy Room</h3>
                    <p>Anonymous self-talk with an AI therapist trained in emotional healing and deep listening.</p>
                    <div class="feature-tags">
                        <span class="tag">Voice & Text</span>
                        <span class="tag">24/7 Available</span>
                    </div>
                </div>

                <div class="feature-card" data-feature="psychology">
                    <div class="feature-icon">📚</div>
                    <h3>Dark Psychology Vault</h3>
                    <p>Unlock powerful concepts in body language, persuasion, and emotional control as you progress.</p>
                    <div class="feature-tags">
                        <span class="tag">Progressive Unlock</span>
                        <span class="tag">Expert Content</span>
                    </div>
                </div>

                <div class="feature-card" data-feature="discipline">
                    <div class="feature-icon">🔥</div>
                    <h3>Discipline Lab</h3>
                    <p>Daily 1-minute challenges: silent hours, dopamine detox, rejection simulation, breath work.</p>
                    <div class="feature-tags">
                        <span class="tag">Daily Challenges</span>
                        <span class="tag">Streak System</span>
                    </div>
                </div>

                <div class="feature-card" data-feature="manmode">
                    <div class="feature-icon">👑</div>
                    <h3>Man Mode</h3>
                    <p>Roadmap from emotional weakness to mental strength. Build masculine leadership and inner peace.</p>
                    <div class="feature-tags">
                        <span class="tag">Male-Focused</span>
                        <span class="tag">Structured Path</span>
                    </div>
                </div>

                <div class="feature-card" data-feature="mood">
                    <div class="feature-icon">📊</div>
                    <h3>Mood Mirror</h3>
                    <p>Track emotional progress with visual graphs showing your journey to mental clarity.</p>
                    <div class="feature-tags">
                        <span class="tag">Visual Progress</span>
                        <span class="tag">Daily Tracking</span>
                    </div>
                </div>

                <div class="feature-card" data-feature="silence">
                    <div class="feature-icon">🎧</div>
                    <h3>Silence Space</h3>
                    <p>Calming digital room with lo-fi music, ambient sounds, and guided breathing.</p>
                    <div class="feature-tags">
                        <span class="tag">Ambient Audio</span>
                        <span class="tag">Meditation</span>
                    </div>
                </div>

            </div>
        </section>

        <!-- Quick Access Dashboard -->
        <section class="dashboard" id="dashboard" style="display: none;">
            <h2 class="section-title">Your Mental Transformation Dashboard</h2>
            <div class="dashboard-grid">
                
                <div class="dashboard-card" onclick="navigateTo('therapy-room')">
                    <div class="card-header">
                        <span class="card-icon">🧠</span>
                        <h3>Therapy Room</h3>
                    </div>
                    <p>Start an anonymous session with your AI therapist</p>
                    <div class="card-status">
                        <span class="status-indicator ready"></span>
                        <span>Ready to listen</span>
                    </div>
                </div>

                <div class="dashboard-card" onclick="navigateTo('discipline-lab')">
                    <div class="card-header">
                        <span class="card-icon">🔥</span>
                        <h3>Discipline Lab</h3>
                    </div>
                    <p>Today's challenge: <span id="daily-challenge">Loading...</span></p>
                    <div class="card-status">
                        <span class="status-indicator" id="challenge-status"></span>
                        <span id="streak-count">0 day streak</span>
                    </div>
                </div>

                <div class="dashboard-card" onclick="navigateTo('psychology-vault')">
                    <div class="card-header">
                        <span class="card-icon">📚</span>
                        <h3>Psychology Vault</h3>
                    </div>
                    <p>Unlock deeper psychological truths</p>
                    <div class="card-status">
                        <span class="unlock-progress" id="vault-progress">3/12 unlocked</span>
                    </div>
                </div>

                <div class="dashboard-card" onclick="navigateTo('man-mode')">
                    <div class="card-header">
                        <span class="card-icon">👑</span>
                        <h3>Man Mode</h3>
                    </div>
                    <p>Your masculinity development path</p>
                    <div class="card-status">
                        <span class="level-indicator" id="man-level">Level 1</span>
                    </div>
                </div>

                <div class="dashboard-card" onclick="navigateTo('mood-mirror')">
                    <div class="card-header">
                        <span class="card-icon">📊</span>
                        <h3>Mood Mirror</h3>
                    </div>
                    <p>Track your emotional journey</p>
                    <div class="card-status">
                        <span class="mood-today" id="mood-today">How are you feeling?</span>
                    </div>
                </div>

                <div class="dashboard-card" onclick="navigateTo('silence-space')">
                    <div class="card-header">
                        <span class="card-icon">🎧</span>
                        <h3>Silence Space</h3>
                    </div>
                    <p>Find peace in the digital calm</p>
                    <div class="card-status">
                        <span class="status-indicator calm"></span>
                        <span>Enter serenity</span>
                    </div>
                </div>

            </div>
        </section>

        <!-- Settings Panel -->
        <div class="settings-panel" id="settings-panel">
            <div class="settings-content">
                <h3>Settings</h3>
                <div class="setting-group">
                    <label>AI Personality</label>
                    <select id="ai-mode">
                        <option value="gentle">Gentle Therapist</option>
                        <option value="tough">Tough Coach</option>
                    </select>
                </div>
                <div class="setting-group">
                    <label>Voice Output</label>
                    <input type="checkbox" id="voice-enabled" checked>
                </div>
                <div class="setting-group">
                    <label>Anonymous Mode</label>
                    <input type="checkbox" id="anonymous-mode" checked>
                </div>
                <button class="btn btn-secondary" onclick="closeSettings()">Close</button>
            </div>
        </div>
    </div>

    <!-- Navigation -->
    <nav class="bottom-nav" id="bottom-nav" style="display: none;">
        <button class="nav-item" onclick="showDashboard()">
            <span class="nav-icon">🏠</span>
            <span class="nav-label">Home</span>
        </button>
        <button class="nav-item" onclick="navigateTo('therapy-room')">
            <span class="nav-icon">🧠</span>
            <span class="nav-label">Therapy</span>
        </button>
        <button class="nav-item" onclick="navigateTo('discipline-lab')">
            <span class="nav-icon">🔥</span>
            <span class="nav-label">Discipline</span>
        </button>
        <button class="nav-item" onclick="showSettings()">
            <span class="nav-icon">⚙️</span>
            <span class="nav-label">Settings</span>
        </button>
    </nav>

    <script src="js/storage.js"></script>
    <script src="js/main.js"></script>
</body>
</html>
