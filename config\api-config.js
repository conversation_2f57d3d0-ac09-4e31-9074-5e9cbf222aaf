// API Configuration for Rewire Platform
class APIConfig {
    constructor() {
        this.endpoints = {
            openai: 'https://api.openai.com/v1/chat/completions',
            backup: 'https://api.anthropic.com/v1/messages' // Fallback option
        };
        
        // Note: In production, API keys should be handled server-side
        // This is for demonstration purposes only
        this.apiKey = null;
        this.model = 'gpt-3.5-turbo';
        this.maxTokens = 500;
        this.temperature = 0.7;
        
        this.init();
    }

    init() {
        // Check if API key is stored (user would need to provide their own)
        this.apiKey = localStorage.getItem('rewire_api_key');
        
        if (!this.apiKey) {
            this.showAPIKeyPrompt();
        }
    }

    showAPIKeyPrompt() {
        const modal = document.createElement('div');
        modal.className = 'api-key-modal';
        modal.innerHTML = `
            <div class="modal-overlay">
                <div class="modal-content">
                    <h3>🔑 API Key Required</h3>
                    <p>To use AI features, please provide your OpenAI API key:</p>
                    <input type="password" id="api-key-input" placeholder="sk-..." />
                    <div class="modal-actions">
                        <button onclick="this.closest('.api-key-modal').remove()">Skip</button>
                        <button onclick="apiConfig.setAPIKey()">Save Key</button>
                    </div>
                    <div class="api-info">
                        <p><small>Your API key is stored locally and never shared. Get one at <a href="https://platform.openai.com/api-keys" target="_blank">OpenAI</a></small></p>
                    </div>
                </div>
            </div>
        `;
        
        document.body.appendChild(modal);
    }

    setAPIKey() {
        const input = document.getElementById('api-key-input');
        const key = input.value.trim();
        
        if (key && key.startsWith('sk-')) {
            this.apiKey = key;
            localStorage.setItem('rewire_api_key', key);
            document.querySelector('.api-key-modal').remove();
            this.showNotification('API key saved successfully!', 'success');
        } else {
            this.showNotification('Please enter a valid OpenAI API key', 'error');
        }
    }

    async makeAIRequest(messages, options = {}) {
        if (!this.apiKey) {
            return this.getFallbackResponse(messages[messages.length - 1].content);
        }

        const requestBody = {
            model: options.model || this.model,
            messages: messages,
            max_tokens: options.maxTokens || this.maxTokens,
            temperature: options.temperature || this.temperature,
            stream: false
        };

        try {
            const response = await fetch(this.endpoints.openai, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${this.apiKey}`
                },
                body: JSON.stringify(requestBody)
            });

            if (!response.ok) {
                throw new Error(`API request failed: ${response.status}`);
            }

            const data = await response.json();
            return data.choices[0].message.content;

        } catch (error) {
            console.error('AI API Error:', error);
            return this.getFallbackResponse(messages[messages.length - 1].content);
        }
    }

    getFallbackResponse(userMessage) {
        // Fallback responses when API is not available
        const responses = {
            greeting: [
                "Hello! I'm here to listen and support you. What's on your mind today?",
                "Welcome to your safe space. How are you feeling right now?",
                "I'm glad you're here. What would you like to talk about?"
            ],
            sadness: [
                "I hear that you're going through a difficult time. It's okay to feel sad - these emotions are valid and part of being human.",
                "Sadness can feel overwhelming, but remember that it's temporary. What's one small thing that might bring you a moment of peace today?",
                "Your feelings matter, and it's brave of you to acknowledge them. Would you like to explore what's behind these feelings?"
            ],
            anxiety: [
                "Anxiety can feel intense, but you're not alone in this. Let's take it one step at a time. What's one thing you can control right now?",
                "I understand that anxiety can be overwhelming. Sometimes focusing on your breath can help ground you in the present moment.",
                "Anxiety often comes from uncertainty about the future. What would help you feel more secure in this moment?"
            ],
            anger: [
                "I can sense your frustration. Anger often tells us something important about our boundaries or values. What do you think this feeling is trying to communicate?",
                "It's natural to feel angry sometimes. What matters is how we channel that energy. What would a constructive response look like for you?",
                "Your anger is valid. Let's explore what's underneath it - sometimes anger protects other vulnerable feelings."
            ],
            confusion: [
                "Feeling confused or lost is part of the human experience. Sometimes clarity comes not from having all the answers, but from taking the next small step.",
                "It's okay not to have everything figured out. What feels most true or important to you right now?",
                "Confusion can be uncomfortable, but it often precedes growth and new understanding. What questions are you sitting with?"
            ],
            motivation: [
                "Building discipline starts with small, consistent actions. What's one tiny step you could take today?",
                "Motivation comes and goes, but discipline creates the foundation for lasting change. What matters most to you right now?",
                "Every expert was once a beginner. What's one area where you'd like to build more strength?"
            ],
            relationships: [
                "Relationships can be complex and challenging. What would an ideal outcome look like for you in this situation?",
                "Communication is often the bridge to understanding. Have you been able to express your feelings clearly?",
                "Healthy relationships require both boundaries and vulnerability. What feels most important to focus on right now?"
            ],
            default: [
                "I'm here to listen. Can you tell me more about what's on your mind?",
                "That sounds important to you. Would you like to explore this further?",
                "I hear you. What feels most significant about this situation?",
                "Thank you for sharing that with me. How are you feeling about it?",
                "What would be most helpful for you to focus on right now?"
            ]
        };

        // Simple keyword matching for appropriate responses
        const lowerMessage = userMessage.toLowerCase();
        let responseCategory = 'default';

        if (lowerMessage.includes('hello') || lowerMessage.includes('hi') || lowerMessage.includes('hey')) {
            responseCategory = 'greeting';
        } else if (lowerMessage.includes('sad') || lowerMessage.includes('depressed') || lowerMessage.includes('down')) {
            responseCategory = 'sadness';
        } else if (lowerMessage.includes('anxious') || lowerMessage.includes('worried') || lowerMessage.includes('stress')) {
            responseCategory = 'anxiety';
        } else if (lowerMessage.includes('angry') || lowerMessage.includes('mad') || lowerMessage.includes('frustrated')) {
            responseCategory = 'anger';
        } else if (lowerMessage.includes('confused') || lowerMessage.includes('lost') || lowerMessage.includes('direction')) {
            responseCategory = 'confusion';
        } else if (lowerMessage.includes('motivation') || lowerMessage.includes('discipline') || lowerMessage.includes('strength')) {
            responseCategory = 'motivation';
        } else if (lowerMessage.includes('relationship') || lowerMessage.includes('friend') || lowerMessage.includes('family')) {
            responseCategory = 'relationships';
        }

        const categoryResponses = responses[responseCategory];
        return categoryResponses[Math.floor(Math.random() * categoryResponses.length)];
    }

    // Therapy-specific prompts
    getTherapySystemPrompt(isToughMode = false) {
        if (isToughMode) {
            return {
                role: "system",
                content: `You are a direct, no-nonsense mental strength coach. Your approach is firm but caring. You:
                - Cut through excuses and self-pity
                - Focus on actionable solutions
                - Challenge users to take responsibility
                - Use tough love to motivate growth
                - Are brutally honest but supportive
                - Push users out of their comfort zones
                - Emphasize discipline over motivation
                
                Keep responses concise and impactful. Always end with a direct question or challenge.`
            };
        } else {
            return {
                role: "system",
                content: `You are a compassionate, empathetic therapist specializing in emotional support and mental wellness. You:
                - Listen without judgment
                - Validate emotions while encouraging growth
                - Ask thoughtful, open-ended questions
                - Provide gentle guidance and insights
                - Help users explore their feelings safely
                - Offer practical coping strategies
                - Create a safe, supportive environment
                
                Keep responses warm, understanding, and focused on the user's emotional well-being.`
            };
        }
    }

    // Psychology content generation
    async generatePsychologyContent(topic, level = 'beginner') {
        const prompt = `Create educational content about ${topic} for ${level} level. Include:
        1. Key concepts
        2. Practical applications
        3. Real-world examples
        4. Action steps
        
        Keep it engaging and actionable.`;

        return await this.makeAIRequest([
            { role: "system", content: "You are an expert in psychology and human behavior." },
            { role: "user", content: prompt }
        ]);
    }

    // Discipline challenge generation
    async generateCustomChallenge(category, difficulty, duration) {
        const prompt = `Create a ${difficulty} difficulty ${category} discipline challenge that takes ${duration} minutes. Include:
        1. Clear instructions
        2. Purpose/benefits
        3. Tips for success
        4. Variations if needed`;

        return await this.makeAIRequest([
            { role: "system", content: "You are a discipline and mental strength expert." },
            { role: "user", content: prompt }
        ]);
    }

    // Mood insights
    async generateMoodInsights(moodData) {
        const prompt = `Based on this mood data: ${JSON.stringify(moodData)}, provide:
        1. Patterns you notice
        2. Potential triggers
        3. Recommendations for improvement
        4. Positive trends to celebrate`;

        return await this.makeAIRequest([
            { role: "system", content: "You are a mental health analyst providing insights." },
            { role: "user", content: prompt }
        ]);
    }

    showNotification(message, type = 'info') {
        if (window.RewireUtils) {
            RewireUtils.showNotification(message, type);
        } else {
            console.log(`${type.toUpperCase()}: ${message}`);
        }
    }
}

// Initialize API configuration
const apiConfig = new APIConfig();

// Export for use in other modules
window.APIConfig = apiConfig;

// Add CSS for API key modal
const style = document.createElement('style');
style.textContent = `
.api-key-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 10000;
}

.modal-overlay {
    background: rgba(0, 0, 0, 0.8);
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
}

.api-key-modal .modal-content {
    background: var(--bg-secondary, #1a1a1a);
    padding: 30px;
    border-radius: 12px;
    max-width: 400px;
    width: 100%;
    text-align: center;
    border: 1px solid var(--bg-tertiary, #2a2a2a);
}

.api-key-modal h3 {
    color: var(--text-primary, #ffffff);
    margin-bottom: 15px;
}

.api-key-modal p {
    color: var(--text-secondary, #b0b0b0);
    margin-bottom: 20px;
}

.api-key-modal input {
    width: 100%;
    padding: 12px;
    background: var(--bg-primary, #0a0a0a);
    border: 1px solid var(--bg-tertiary, #2a2a2a);
    border-radius: 8px;
    color: var(--text-primary, #ffffff);
    margin-bottom: 20px;
    font-family: monospace;
}

.modal-actions {
    display: flex;
    gap: 10px;
    justify-content: center;
    margin-bottom: 15px;
}

.modal-actions button {
    padding: 10px 20px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-weight: 500;
}

.modal-actions button:first-child {
    background: var(--bg-tertiary, #2a2a2a);
    color: var(--text-secondary, #b0b0b0);
}

.modal-actions button:last-child {
    background: var(--accent-primary, #6366f1);
    color: white;
}

.api-info {
    font-size: 12px;
    color: var(--text-muted, #666666);
}

.api-info a {
    color: var(--accent-primary, #6366f1);
    text-decoration: none;
}
`;

document.head.appendChild(style);
