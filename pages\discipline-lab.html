<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Discipline Lab - Rewire</title>
    <link rel="stylesheet" href="../css/main.css">
    <link rel="stylesheet" href="../css/discipline.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <div class="discipline-container">
        <!-- Header -->
        <header class="discipline-header">
            <button class="back-btn" onclick="goBack()">
                <span class="back-icon">←</span>
                <span>Back</span>
            </button>
            <h1 class="discipline-title">
                <span class="discipline-icon">🔥</span>
                Discipline Lab
            </h1>
            <div class="streak-display">
                <span class="streak-number" id="streak-number">0</span>
                <span class="streak-label">Day Streak</span>
            </div>
        </header>

        <!-- Main Content -->
        <main class="discipline-main">
            <!-- Today's Challenge -->
            <section class="daily-challenge">
                <div class="challenge-card">
                    <div class="challenge-header">
                        <h2>Today's Challenge</h2>
                        <div class="challenge-date" id="challenge-date"></div>
                    </div>
                    
                    <div class="challenge-content">
                        <div class="challenge-icon" id="challenge-icon">💪</div>
                        <h3 class="challenge-name" id="challenge-name">Loading...</h3>
                        <p class="challenge-description" id="challenge-description">Loading challenge...</p>
                        
                        <div class="challenge-details">
                            <div class="detail-item">
                                <span class="detail-label">Duration:</span>
                                <span class="detail-value" id="challenge-duration">5 min</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">Difficulty:</span>
                                <span class="detail-value" id="challenge-difficulty">Medium</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">Points:</span>
                                <span class="detail-value" id="challenge-points">15</span>
                            </div>
                        </div>
                        
                        <div class="challenge-actions">
                            <button class="btn btn-primary challenge-btn" id="start-challenge" onclick="startChallenge()">
                                Start Challenge
                            </button>
                            <button class="btn btn-secondary" onclick="skipChallenge()">
                                Skip Today
                            </button>
                        </div>
                        
                        <div class="challenge-timer" id="challenge-timer" style="display: none;">
                            <div class="timer-display">
                                <span class="timer-minutes" id="timer-minutes">05</span>
                                <span class="timer-separator">:</span>
                                <span class="timer-seconds" id="timer-seconds">00</span>
                            </div>
                            <div class="timer-controls">
                                <button class="timer-btn" onclick="pauseTimer()">⏸️</button>
                                <button class="timer-btn" onclick="stopTimer()">⏹️</button>
                                <button class="timer-btn" onclick="completeChallenge()">✅</button>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Progress Overview -->
            <section class="progress-overview">
                <h2>Your Progress</h2>
                <div class="progress-grid">
                    <div class="progress-card">
                        <div class="progress-icon">🏆</div>
                        <div class="progress-content">
                            <h3>Total Points</h3>
                            <span class="progress-value" id="total-points">0</span>
                        </div>
                    </div>
                    
                    <div class="progress-card">
                        <div class="progress-icon">📅</div>
                        <div class="progress-content">
                            <h3>Challenges Completed</h3>
                            <span class="progress-value" id="completed-challenges">0</span>
                        </div>
                    </div>
                    
                    <div class="progress-card">
                        <div class="progress-icon">🎯</div>
                        <div class="progress-content">
                            <h3>Success Rate</h3>
                            <span class="progress-value" id="success-rate">0%</span>
                        </div>
                    </div>
                    
                    <div class="progress-card">
                        <div class="progress-icon">⚡</div>
                        <div class="progress-content">
                            <h3>Longest Streak</h3>
                            <span class="progress-value" id="longest-streak">0</span>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Challenge Categories -->
            <section class="challenge-categories">
                <h2>Challenge Categories</h2>
                <div class="categories-grid">
                    
                    <div class="category-card" data-category="mental">
                        <div class="category-icon">🧠</div>
                        <h3>Mental Discipline</h3>
                        <p>Silence, meditation, focus exercises</p>
                        <div class="category-progress">
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: 30%"></div>
                            </div>
                            <span class="progress-text">3/10 completed</span>
                        </div>
                    </div>
                    
                    <div class="category-card" data-category="physical">
                        <div class="category-icon">💪</div>
                        <h3>Physical Discipline</h3>
                        <p>Cold exposure, breath work, exercise</p>
                        <div class="category-progress">
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: 20%"></div>
                            </div>
                            <span class="progress-text">2/10 completed</span>
                        </div>
                    </div>
                    
                    <div class="category-card" data-category="social">
                        <div class="category-icon">👥</div>
                        <h3>Social Discipline</h3>
                        <p>Rejection practice, communication</p>
                        <div class="category-progress">
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: 10%"></div>
                            </div>
                            <span class="progress-text">1/10 completed</span>
                        </div>
                    </div>
                    
                    <div class="category-card" data-category="digital">
                        <div class="category-icon">📱</div>
                        <h3>Digital Discipline</h3>
                        <p>Dopamine detox, screen time limits</p>
                        <div class="category-progress">
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: 40%"></div>
                            </div>
                            <span class="progress-text">4/10 completed</span>
                        </div>
                    </div>
                    
                </div>
            </section>

            <!-- Weekly Calendar -->
            <section class="weekly-calendar">
                <h2>This Week's Progress</h2>
                <div class="calendar-grid" id="calendar-grid">
                    <!-- Calendar will be populated by JavaScript -->
                </div>
            </section>

            <!-- Achievements -->
            <section class="achievements">
                <h2>Achievements</h2>
                <div class="achievements-grid" id="achievements-grid">
                    
                    <div class="achievement-card locked">
                        <div class="achievement-icon">🔥</div>
                        <h3>First Flame</h3>
                        <p>Complete your first challenge</p>
                        <div class="achievement-progress">0/1</div>
                    </div>
                    
                    <div class="achievement-card locked">
                        <div class="achievement-icon">⚡</div>
                        <h3>Week Warrior</h3>
                        <p>Complete 7 challenges in a row</p>
                        <div class="achievement-progress">0/7</div>
                    </div>
                    
                    <div class="achievement-card locked">
                        <div class="achievement-icon">💎</div>
                        <h3>Diamond Mind</h3>
                        <p>Reach 30-day streak</p>
                        <div class="achievement-progress">0/30</div>
                    </div>
                    
                    <div class="achievement-card locked">
                        <div class="achievement-icon">👑</div>
                        <h3>Discipline Master</h3>
                        <p>Complete 100 challenges</p>
                        <div class="achievement-progress">0/100</div>
                    </div>
                    
                </div>
            </section>
        </main>

        <!-- Challenge Modal -->
        <div class="challenge-modal" id="challenge-modal" style="display: none;">
            <div class="modal-content">
                <div class="modal-header">
                    <h3>Challenge Instructions</h3>
                    <button class="close-btn" onclick="closeModal()">×</button>
                </div>
                <div class="modal-body">
                    <div class="instruction-content" id="instruction-content">
                        <!-- Instructions will be populated by JavaScript -->
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" onclick="closeModal()">Cancel</button>
                    <button class="btn btn-primary" onclick="beginChallenge()">Begin Challenge</button>
                </div>
            </div>
        </div>

        <!-- Completion Modal -->
        <div class="completion-modal" id="completion-modal" style="display: none;">
            <div class="modal-content">
                <div class="modal-header">
                    <h3>🎉 Challenge Completed!</h3>
                </div>
                <div class="modal-body">
                    <div class="completion-content">
                        <div class="completion-stats">
                            <div class="stat">
                                <span class="stat-label">Points Earned:</span>
                                <span class="stat-value" id="earned-points">+15</span>
                            </div>
                            <div class="stat">
                                <span class="stat-label">New Streak:</span>
                                <span class="stat-value" id="new-streak">5 days</span>
                            </div>
                        </div>
                        <div class="completion-message" id="completion-message">
                            Great job! You're building mental strength one challenge at a time.
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-primary" onclick="closeCompletionModal()">Continue</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="../js/storage.js"></script>
    <script src="../js/discipline-lab.js"></script>
    <script>
        // Navigation
        function goBack() {
            window.history.back();
        }

        // Initialize discipline lab
        document.addEventListener('DOMContentLoaded', () => {
            if (window.DisciplineLab) {
                window.disciplineLab = new DisciplineLab();
            }
        });

        // Global functions
        function startChallenge() {
            if (window.disciplineLab) {
                window.disciplineLab.startChallenge();
            }
        }

        function skipChallenge() {
            if (window.disciplineLab) {
                window.disciplineLab.skipChallenge();
            }
        }

        function completeChallenge() {
            if (window.disciplineLab) {
                window.disciplineLab.completeChallenge();
            }
        }

        function pauseTimer() {
            if (window.disciplineLab) {
                window.disciplineLab.pauseTimer();
            }
        }

        function stopTimer() {
            if (window.disciplineLab) {
                window.disciplineLab.stopTimer();
            }
        }

        function closeModal() {
            document.getElementById('challenge-modal').style.display = 'none';
        }

        function beginChallenge() {
            closeModal();
            if (window.disciplineLab) {
                window.disciplineLab.beginChallenge();
            }
        }

        function closeCompletionModal() {
            document.getElementById('completion-modal').style.display = 'none';
        }
    </script>
</body>
</html>
