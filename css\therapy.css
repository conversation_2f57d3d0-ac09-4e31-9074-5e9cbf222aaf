/* Therapy Room Specific Styles */
.therapy-container {
    display: grid;
    grid-template-areas: 
        "header header"
        "main sidebar";
    grid-template-columns: 1fr 300px;
    grid-template-rows: auto 1fr;
    height: 100vh;
    background: var(--bg-primary);
    position: relative;
}

/* Header */
.therapy-header {
    grid-area: header;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-md) var(--spacing-xl);
    background: var(--bg-secondary);
    border-bottom: 1px solid var(--bg-tertiary);
}

.back-btn {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-md);
    transition: all 0.3s ease;
}

.back-btn:hover {
    background: var(--bg-tertiary);
    color: var(--text-primary);
}

.therapy-title {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-primary);
}

.therapy-status {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    color: var(--text-secondary);
    font-size: 0.875rem;
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: var(--accent-success);
    animation: pulse-green 2s ease-in-out infinite;
}

/* Main Chat Area */
.therapy-main {
    grid-area: main;
    display: flex;
    flex-direction: column;
    height: calc(100vh - 80px);
    overflow: hidden;
}

.chat-container {
    flex: 1;
    overflow-y: auto;
    padding: var(--spacing-xl);
    scroll-behavior: smooth;
}

.welcome-message {
    display: flex;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-xl);
    padding: var(--spacing-xl);
    background: var(--bg-secondary);
    border-radius: var(--radius-lg);
    border-left: 4px solid var(--accent-primary);
}

.ai-avatar {
    font-size: 2rem;
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--bg-tertiary);
    border-radius: 50%;
    flex-shrink: 0;
}

.message-content h3 {
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
    font-size: 1.25rem;
}

.message-content p {
    color: var(--text-secondary);
    line-height: 1.6;
    margin-bottom: var(--spacing-lg);
}

.quick-starters {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.starter-btn {
    background: var(--bg-primary);
    border: 1px solid var(--bg-tertiary);
    color: var(--text-secondary);
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: left;
}

.starter-btn:hover {
    background: var(--bg-tertiary);
    color: var(--text-primary);
    border-color: var(--accent-primary);
}

/* Chat Messages */
.message {
    display: flex;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
    animation: fadeInUp 0.3s ease;
}

.message.user {
    flex-direction: row-reverse;
}

.message-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    flex-shrink: 0;
}

.message.ai .message-avatar {
    background: var(--bg-tertiary);
}

.message.user .message-avatar {
    background: var(--accent-primary);
}

.message-bubble {
    max-width: 70%;
    padding: var(--spacing-md) var(--spacing-lg);
    border-radius: var(--radius-lg);
    position: relative;
}

.message.ai .message-bubble {
    background: var(--bg-secondary);
    border-bottom-left-radius: var(--radius-sm);
}

.message.user .message-bubble {
    background: var(--accent-primary);
    color: white;
    border-bottom-right-radius: var(--radius-sm);
}

.message-text {
    line-height: 1.6;
    word-wrap: break-word;
}

.message-time {
    font-size: 0.75rem;
    opacity: 0.7;
    margin-top: var(--spacing-xs);
}

/* Input Area */
.input-area {
    padding: var(--spacing-lg) var(--spacing-xl);
    background: var(--bg-secondary);
    border-top: 1px solid var(--bg-tertiary);
}

.input-container {
    display: flex;
    align-items: flex-end;
    gap: var(--spacing-md);
    background: var(--bg-primary);
    border: 1px solid var(--bg-tertiary);
    border-radius: var(--radius-lg);
    padding: var(--spacing-md);
    transition: border-color 0.3s ease;
}

.input-container:focus-within {
    border-color: var(--accent-primary);
}

.voice-btn, .send-btn {
    width: 40px;
    height: 40px;
    border: none;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    transition: all 0.3s ease;
    flex-shrink: 0;
}

.voice-btn {
    background: var(--bg-tertiary);
    color: var(--text-secondary);
}

.voice-btn:hover,
.voice-btn.active {
    background: var(--accent-primary);
    color: white;
}

.send-btn {
    background: var(--accent-primary);
    color: white;
}

.send-btn:hover {
    background: var(--accent-secondary);
    transform: scale(1.05);
}

.send-btn:disabled {
    background: var(--bg-tertiary);
    color: var(--text-muted);
    cursor: not-allowed;
    transform: none;
}

#message-input {
    flex: 1;
    background: none;
    border: none;
    color: var(--text-primary);
    font-size: 1rem;
    line-height: 1.5;
    resize: none;
    outline: none;
    min-height: 24px;
    max-height: 120px;
    font-family: inherit;
}

#message-input::placeholder {
    color: var(--text-muted);
}

.input-options {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: var(--spacing-md);
    font-size: 0.875rem;
}

.option {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    color: var(--text-secondary);
    cursor: pointer;
}

.option input[type="checkbox"] {
    accent-color: var(--accent-primary);
}

.clear-btn {
    background: none;
    border: none;
    color: var(--text-muted);
    cursor: pointer;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    transition: color 0.3s ease;
}

.clear-btn:hover {
    color: var(--accent-danger);
}

/* Sidebar */
.therapy-sidebar {
    grid-area: sidebar;
    background: var(--bg-secondary);
    border-left: 1px solid var(--bg-tertiary);
    overflow-y: auto;
    transition: transform 0.3s ease;
}

.therapy-sidebar.hidden {
    transform: translateX(100%);
}

.sidebar-content {
    padding: var(--spacing-xl);
}

.sidebar-content h3 {
    color: var(--text-primary);
    margin-bottom: var(--spacing-lg);
    font-size: 1.25rem;
}

.insight-card {
    background: var(--bg-primary);
    padding: var(--spacing-lg);
    border-radius: var(--radius-lg);
    margin-bottom: var(--spacing-lg);
    border: 1px solid var(--bg-tertiary);
}

.insight-card h4 {
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
    font-size: 1rem;
    font-weight: 600;
}

.mood-selector {
    display: flex;
    gap: var(--spacing-sm);
    flex-wrap: wrap;
}

.mood-btn {
    width: 40px;
    height: 40px;
    border: none;
    border-radius: 50%;
    font-size: 1.25rem;
    cursor: pointer;
    background: var(--bg-tertiary);
    transition: all 0.3s ease;
}

.mood-btn:hover,
.mood-btn.active {
    transform: scale(1.1);
    box-shadow: var(--shadow-md);
}

.progress-stats {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.stat {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.stat-label {
    color: var(--text-secondary);
    font-size: 0.875rem;
}

.stat-value {
    color: var(--text-primary);
    font-weight: 600;
}

.themes-list {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-sm);
}

.theme-tag {
    background: var(--bg-tertiary);
    color: var(--text-secondary);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    font-size: 0.75rem;
}

.actions-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.action-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    color: var(--text-secondary);
    font-size: 0.875rem;
}

/* Floating Controls */
.floating-controls {
    position: fixed;
    right: 320px;
    bottom: var(--spacing-xl);
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
    z-index: 100;
}

.control-btn {
    width: 50px;
    height: 50px;
    border: none;
    border-radius: 50%;
    background: var(--accent-primary);
    color: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    box-shadow: var(--shadow-lg);
    transition: all 0.3s ease;
}

.control-btn:hover {
    transform: scale(1.1);
    box-shadow: var(--shadow-glow);
}

/* Voice Indicator */
.voice-indicator {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: var(--bg-secondary);
    padding: var(--spacing-xl);
    border-radius: var(--radius-lg);
    text-align: center;
    box-shadow: var(--shadow-lg);
    z-index: 1000;
    border: 1px solid var(--accent-primary);
}

.voice-animation {
    display: flex;
    justify-content: center;
    gap: var(--spacing-xs);
    margin-bottom: var(--spacing-md);
}

.voice-wave {
    width: 4px;
    height: 20px;
    background: var(--accent-primary);
    border-radius: 2px;
    animation: voice-wave 1s ease-in-out infinite;
}

.voice-wave:nth-child(2) {
    animation-delay: 0.2s;
}

.voice-wave:nth-child(3) {
    animation-delay: 0.4s;
}

@keyframes voice-wave {
    0%, 100% { height: 20px; }
    50% { height: 40px; }
}

.stop-voice-btn {
    background: var(--accent-danger);
    color: white;
    border: none;
    padding: var(--spacing-sm) var(--spacing-lg);
    border-radius: var(--radius-md);
    cursor: pointer;
    margin-top: var(--spacing-md);
}

/* Loading Indicator */
.loading-indicator {
    position: fixed;
    bottom: 100px;
    left: 50%;
    transform: translateX(-50%);
    background: var(--bg-secondary);
    padding: var(--spacing-lg);
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    box-shadow: var(--shadow-md);
    z-index: 100;
}

.loading-dots {
    display: flex;
    gap: var(--spacing-xs);
}

.dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: var(--accent-primary);
    animation: loading-dot 1.4s ease-in-out infinite both;
}

.dot:nth-child(1) { animation-delay: -0.32s; }
.dot:nth-child(2) { animation-delay: -0.16s; }

@keyframes loading-dot {
    0%, 80%, 100% { transform: scale(0); }
    40% { transform: scale(1); }
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .therapy-container {
        grid-template-areas: 
            "header"
            "main";
        grid-template-columns: 1fr;
    }
    
    .therapy-sidebar {
        position: fixed;
        top: 80px;
        right: 0;
        width: 100%;
        height: calc(100vh - 80px);
        z-index: 200;
    }
    
    .floating-controls {
        right: var(--spacing-lg);
    }
    
    .therapy-header {
        padding: var(--spacing-md);
    }
    
    .chat-container {
        padding: var(--spacing-md);
    }
    
    .input-area {
        padding: var(--spacing-md);
    }
}
