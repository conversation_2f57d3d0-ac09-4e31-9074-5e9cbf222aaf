// AI Therapy Room Controller
class TherapyRoom {
    constructor() {
        this.messages = [];
        this.sessionStartTime = new Date();
        this.currentMood = null;
        this.isVoiceRecording = false;
        this.recognition = null;
        this.synthesis = window.speechSynthesis;
        this.messageCount = 0;
        this.themes = new Set();
        this.insights = [];
        
        this.init();
    }

    init() {
        this.setupVoiceRecognition();
        this.loadSession();
        this.updateStats();
        this.startSessionTimer();
        
        // Load user settings
        const settings = RewireStorage.get('settings', {});
        this.isToughMode = settings.aiMode === 'tough';
        this.voiceEnabled = settings.voiceEnabled;
        
        // Update UI based on settings
        document.getElementById('tough-mode').checked = this.isToughMode;
        document.getElementById('voice-response').checked = this.voiceEnabled;
        
        // Setup event listeners
        this.setupEventListeners();
    }

    setupEventListeners() {
        // Tough mode toggle
        document.getElementById('tough-mode').addEventListener('change', (e) => {
            this.isToughMode = e.target.checked;
            this.updateAIPersonality();
        });

        // Voice response toggle
        document.getElementById('voice-response').addEventListener('change', (e) => {
            this.voiceEnabled = e.target.checked;
        });
    }

    setupVoiceRecognition() {
        if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
            const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
            this.recognition = new SpeechRecognition();
            
            this.recognition.continuous = false;
            this.recognition.interimResults = false;
            this.recognition.lang = 'en-US';
            
            this.recognition.onstart = () => {
                this.showVoiceIndicator();
            };
            
            this.recognition.onresult = (event) => {
                const transcript = event.results[0][0].transcript;
                document.getElementById('message-input').value = transcript;
                this.hideVoiceIndicator();
            };
            
            this.recognition.onerror = (event) => {
                console.error('Speech recognition error:', event.error);
                this.hideVoiceIndicator();
                this.showNotification('Voice recognition failed. Please try again.', 'error');
            };
            
            this.recognition.onend = () => {
                this.hideVoiceIndicator();
                this.isVoiceRecording = false;
            };
        }
    }

    async sendMessage() {
        const input = document.getElementById('message-input');
        const message = input.value.trim();
        
        if (!message) return;
        
        // Add user message
        this.addMessage('user', message);
        input.value = '';
        input.style.height = 'auto';
        
        // Show loading
        this.showLoading();
        
        try {
            // Get AI response
            const response = await this.getAIResponse(message);
            this.hideLoading();
            
            // Add AI response
            this.addMessage('ai', response);
            
            // Speak response if enabled
            if (this.voiceEnabled) {
                this.speakText(response);
            }
            
            // Analyze message for themes and insights
            this.analyzeMessage(message);
            
        } catch (error) {
            this.hideLoading();
            console.error('AI response error:', error);
            this.addMessage('ai', "I'm having trouble connecting right now. Please try again in a moment.");
        }
        
        this.updateStats();
        this.saveSession();
    }

    sendQuickMessage(message) {
        document.getElementById('message-input').value = message;
        this.sendMessage();
    }

    async getAIResponse(userMessage) {
        // This is a mock AI response system
        // In a real implementation, you would call OpenAI API or similar
        
        const responses = this.isToughMode ? this.getToughResponses() : this.getGentleResponses();
        
        // Simple keyword-based response selection
        const keywords = userMessage.toLowerCase();
        let response = responses.default;
        
        if (keywords.includes('sad') || keywords.includes('depressed') || keywords.includes('down')) {
            response = responses.sadness;
        } else if (keywords.includes('anxious') || keywords.includes('worried') || keywords.includes('stress')) {
            response = responses.anxiety;
        } else if (keywords.includes('angry') || keywords.includes('mad') || keywords.includes('frustrated')) {
            response = responses.anger;
        } else if (keywords.includes('confused') || keywords.includes('lost') || keywords.includes('direction')) {
            response = responses.confusion;
        } else if (keywords.includes('relationship') || keywords.includes('friend') || keywords.includes('family')) {
            response = responses.relationships;
        } else if (keywords.includes('work') || keywords.includes('job') || keywords.includes('career')) {
            response = responses.work;
        }
        
        // Add personalization based on session history
        if (this.messages.length > 5) {
            response += this.isToughMode ? 
                " You've been talking for a while now. What are you actually going to DO about this?" :
                " I notice we've been exploring this together. What feels most important to focus on right now?";
        }
        
        return response;
    }

    getGentleResponses() {
        return {
            default: "I hear you. Can you tell me more about what's on your mind right now?",
            sadness: "It sounds like you're going through a really difficult time. Those feelings are valid, and it's okay to sit with them. What do you think might help you feel a little lighter today?",
            anxiety: "Anxiety can feel overwhelming. Let's take this one step at a time. What's one small thing that usually helps you feel more grounded?",
            anger: "I can sense your frustration. Anger often tells us something important about our boundaries or values. What do you think this anger is trying to protect?",
            confusion: "Feeling lost is part of the human experience. Sometimes clarity comes not from having all the answers, but from taking the next small step. What feels true for you right now?",
            relationships: "Relationships can be complex and challenging. It sounds like this is weighing on you. What would an ideal outcome look like for you?",
            work: "Work stress can really impact our overall well-being. What aspects of your work situation feel most challenging right now?"
        };
    }

    getToughResponses() {
        return {
            default: "Alright, let's cut through the noise. What's the real issue here, and what are you going to do about it?",
            sadness: "I get it, you're sad. But wallowing won't change anything. What's one action you can take today to move forward? Stop waiting for motivation - discipline creates momentum.",
            anxiety: "Anxiety is just your mind creating problems that don't exist yet. Focus on what you can control RIGHT NOW. What's one thing you can actually do instead of worrying?",
            anger: "Good. Anger means you still care about something. Channel that energy into action instead of just complaining. What are you going to change?",
            confusion: "Confusion is a luxury you can't afford. Pick a direction and start moving. You'll course-correct along the way. What's your next move?",
            relationships: "Stop trying to control other people. Focus on being the kind of person others want to be around. What do YOU need to improve?",
            work: "Work problems are usually mindset problems. Are you bringing your best effort, or are you just going through the motions? Be honest."
        };
    }

    addMessage(sender, text) {
        const chatContainer = document.getElementById('chat-container');
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${sender}`;
        
        const avatar = sender === 'ai' ? '🤖' : '👤';
        const time = new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
        
        messageDiv.innerHTML = `
            <div class="message-avatar">${avatar}</div>
            <div class="message-bubble">
                <div class="message-text">${text}</div>
                <div class="message-time">${time}</div>
            </div>
        `;
        
        // Remove welcome message if it exists
        const welcomeMessage = chatContainer.querySelector('.welcome-message');
        if (welcomeMessage && this.messages.length === 0) {
            welcomeMessage.remove();
        }
        
        chatContainer.appendChild(messageDiv);
        chatContainer.scrollTop = chatContainer.scrollHeight;
        
        // Store message
        this.messages.push({
            sender,
            text,
            timestamp: new Date().toISOString()
        });
        
        this.messageCount++;
    }

    analyzeMessage(message) {
        // Extract themes from user message
        const emotionKeywords = {
            'stress': ['stress', 'stressed', 'pressure', 'overwhelmed'],
            'sadness': ['sad', 'depressed', 'down', 'hopeless'],
            'anxiety': ['anxious', 'worried', 'nervous', 'panic'],
            'anger': ['angry', 'mad', 'frustrated', 'irritated'],
            'relationships': ['relationship', 'friend', 'family', 'partner'],
            'work': ['work', 'job', 'career', 'boss', 'colleague'],
            'identity': ['who am i', 'purpose', 'meaning', 'direction']
        };
        
        const lowerMessage = message.toLowerCase();
        Object.keys(emotionKeywords).forEach(theme => {
            if (emotionKeywords[theme].some(keyword => lowerMessage.includes(keyword))) {
                this.themes.add(theme);
            }
        });
        
        this.updateThemes();
        this.generateInsights();
    }

    updateThemes() {
        const themesList = document.getElementById('themes-list');
        themesList.innerHTML = '';
        
        if (this.themes.size === 0) {
            themesList.innerHTML = '<span class="theme-tag">Getting started...</span>';
            return;
        }
        
        this.themes.forEach(theme => {
            const themeTag = document.createElement('span');
            themeTag.className = 'theme-tag';
            themeTag.textContent = theme.charAt(0).toUpperCase() + theme.slice(1);
            themesList.appendChild(themeTag);
        });
    }

    generateInsights() {
        const actionsList = document.getElementById('actions-list');
        const insights = [];
        
        if (this.themes.has('stress')) {
            insights.push({ icon: '🧘', text: 'Try a 5-minute breathing exercise' });
        }
        if (this.themes.has('relationships')) {
            insights.push({ icon: '💬', text: 'Consider direct communication' });
        }
        if (this.themes.has('work')) {
            insights.push({ icon: '📝', text: 'Write down your priorities' });
        }
        if (this.messageCount > 3) {
            insights.push({ icon: '💪', text: 'Take action on one insight today' });
        }
        
        actionsList.innerHTML = '';
        insights.forEach(insight => {
            const actionDiv = document.createElement('div');
            actionDiv.className = 'action-item';
            actionDiv.innerHTML = `
                <span class="action-icon">${insight.icon}</span>
                <span>${insight.text}</span>
            `;
            actionsList.appendChild(actionDiv);
        });
    }

    toggleVoiceInput() {
        if (!this.recognition) {
            this.showNotification('Voice recognition not supported in this browser', 'error');
            return;
        }
        
        if (this.isVoiceRecording) {
            this.stopVoiceInput();
        } else {
            this.startVoiceInput();
        }
    }

    startVoiceInput() {
        this.isVoiceRecording = true;
        document.getElementById('voice-btn').classList.add('active');
        this.recognition.start();
    }

    stopVoiceInput() {
        this.isVoiceRecording = false;
        document.getElementById('voice-btn').classList.remove('active');
        if (this.recognition) {
            this.recognition.stop();
        }
        this.hideVoiceIndicator();
    }

    speakText(text) {
        if (!this.synthesis) return;
        
        // Cancel any ongoing speech
        this.synthesis.cancel();
        
        const utterance = new SpeechSynthesisUtterance(text);
        utterance.rate = 0.9;
        utterance.pitch = 1;
        utterance.volume = 0.8;
        
        // Use a calming voice if available
        const voices = this.synthesis.getVoices();
        const preferredVoice = voices.find(voice => 
            voice.name.includes('Female') || voice.name.includes('Samantha')
        );
        if (preferredVoice) {
            utterance.voice = preferredVoice;
        }
        
        this.synthesis.speak(utterance);
    }

    setMood(mood) {
        this.currentMood = mood;
        
        // Update mood buttons
        document.querySelectorAll('.mood-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[data-mood="${this.getMoodEmoji(mood)}"]`).classList.add('active');
        
        // Save mood
        const today = RewireUtils.formatDate(new Date());
        RewireStorage.addMoodEntry(today, mood);
        
        this.showNotification(`Mood set to ${mood}`, 'success');
    }

    getMoodEmoji(mood) {
        const moodMap = {
            'sad': '😔',
            'neutral': '😐',
            'happy': '😊',
            'anxious': '😰',
            'angry': '😡'
        };
        return moodMap[mood] || '😐';
    }

    updateStats() {
        document.getElementById('message-count').textContent = this.messageCount;
        document.getElementById('insight-count').textContent = this.insights.length;
        
        const duration = Math.floor((new Date() - this.sessionStartTime) / 60000);
        document.getElementById('session-duration').textContent = `${duration}m`;
    }

    startSessionTimer() {
        setInterval(() => {
            this.updateStats();
        }, 60000); // Update every minute
    }

    showVoiceIndicator() {
        document.getElementById('voice-indicator').style.display = 'block';
    }

    hideVoiceIndicator() {
        document.getElementById('voice-indicator').style.display = 'none';
    }

    showLoading() {
        document.getElementById('loading-indicator').style.display = 'flex';
    }

    hideLoading() {
        document.getElementById('loading-indicator').style.display = 'none';
    }

    clearChat() {
        if (confirm('Are you sure you want to clear this chat session?')) {
            this.messages = [];
            this.messageCount = 0;
            this.themes.clear();
            
            const chatContainer = document.getElementById('chat-container');
            chatContainer.innerHTML = `
                <div class="welcome-message">
                    <div class="ai-avatar">🤖</div>
                    <div class="message-content">
                        <h3>Welcome back to your safe space</h3>
                        <p>I'm here to listen. What's on your mind today?</p>
                    </div>
                </div>
            `;
            
            this.updateStats();
            this.updateThemes();
        }
    }

    saveSession() {
        const sessionData = {
            messages: this.messages,
            startTime: this.sessionStartTime,
            mood: this.currentMood,
            themes: Array.from(this.themes),
            duration: Math.floor((new Date() - this.sessionStartTime) / 60000)
        };
        
        RewireStorage.addTherapySession(sessionData);
        this.showNotification('Session saved successfully', 'success');
    }

    exportChat() {
        const chatText = this.messages.map(msg => 
            `${msg.sender.toUpperCase()}: ${msg.text}`
        ).join('\n\n');
        
        const blob = new Blob([chatText], { type: 'text/plain' });
        const url = URL.createObjectURL(blob);
        
        const a = document.createElement('a');
        a.href = url;
        a.download = `therapy-session-${new Date().toISOString().split('T')[0]}.txt`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    }

    loadSession() {
        // Load any existing session data if needed
        const therapy = RewireStorage.get('therapy', {});
        if (therapy.sessions && therapy.sessions.length > 0) {
            // Could restore last session or show session history
        }
    }

    updateAIPersonality() {
        const settings = RewireStorage.get('settings', {});
        settings.aiMode = this.isToughMode ? 'tough' : 'gentle';
        RewireStorage.set('settings', settings);
        
        const modeText = this.isToughMode ? 'Tough Coach' : 'Gentle Therapist';
        this.showNotification(`AI mode changed to ${modeText}`, 'info');
    }

    showNotification(message, type = 'info') {
        if (window.RewireUtils) {
            RewireUtils.showNotification(message, type);
        }
    }
}

// Export for global use
window.TherapyRoom = TherapyRoom;
