// Discipline Lab Controller
class DisciplineLab {
    constructor() {
        this.currentChallenge = null;
        this.timer = null;
        this.timerInterval = null;
        this.isPaused = false;
        this.startTime = null;
        this.challenges = this.initializeChallenges();
        this.userProgress = this.loadProgress();
        
        this.init();
    }

    init() {
        this.loadTodaysChallenge();
        this.updateProgress();
        this.renderCalendar();
        this.updateAchievements();
        this.updateStreak();
    }

    initializeChallenges() {
        return {
            mental: [
                {
                    id: 'silence_5min',
                    name: '5-Minute Silence',
                    description: 'Sit in complete silence without any distractions. No phone, no music, just you and your thoughts.',
                    duration: 5,
                    difficulty: 'Easy',
                    points: 10,
                    icon: '🤫',
                    instructions: 'Find a quiet space, sit comfortably, and remain silent for 5 minutes. Observe your thoughts without judgment.',
                    category: 'mental'
                },
                {
                    id: 'meditation_10min',
                    name: '10-Minute Meditation',
                    description: 'Focus on your breath for 10 minutes. When your mind wanders, gently bring it back.',
                    duration: 10,
                    difficulty: 'Medium',
                    points: 15,
                    icon: '🧘',
                    instructions: 'Sit with your back straight, close your eyes, and focus on your breathing. Count breaths from 1 to 10, then repeat.',
                    category: 'mental'
                },
                {
                    id: 'focus_task',
                    name: 'Single-Task Focus',
                    description: 'Complete one task with complete focus for 25 minutes. No multitasking allowed.',
                    duration: 25,
                    difficulty: 'Hard',
                    points: 25,
                    icon: '🎯',
                    instructions: 'Choose one important task. Turn off all notifications and work on only that task for 25 minutes.',
                    category: 'mental'
                }
            ],
            physical: [
                {
                    id: 'cold_shower',
                    name: 'Cold Shower',
                    description: 'Take a cold shower for at least 2 minutes. Build physical and mental resilience.',
                    duration: 2,
                    difficulty: 'Medium',
                    points: 20,
                    icon: '🚿',
                    instructions: 'Start with warm water, then gradually turn it cold. Stay under cold water for at least 2 minutes.',
                    category: 'physical'
                },
                {
                    id: 'breath_hold',
                    name: 'Breath Control',
                    description: 'Practice breath holding exercises. Hold your breath for 30 seconds, 5 times.',
                    duration: 10,
                    difficulty: 'Medium',
                    points: 15,
                    icon: '💨',
                    instructions: 'Take a deep breath, hold for 30 seconds, then breathe normally for 1 minute. Repeat 5 times.',
                    category: 'physical'
                },
                {
                    id: 'exercise_burst',
                    name: '10-Minute Exercise',
                    description: 'Do 10 minutes of intense physical exercise. Push your limits.',
                    duration: 10,
                    difficulty: 'Hard',
                    points: 20,
                    icon: '💪',
                    instructions: 'Choose high-intensity exercises: burpees, push-ups, squats. Go all out for 10 minutes.',
                    category: 'physical'
                }
            ],
            social: [
                {
                    id: 'rejection_practice',
                    name: 'Rejection Practice',
                    description: 'Ask for something you expect to be denied. Build rejection resilience.',
                    duration: 5,
                    difficulty: 'Hard',
                    points: 30,
                    icon: '🙋',
                    instructions: 'Ask a stranger for a small favor, request a discount, or make an unusual request. Embrace the "no".',
                    category: 'social'
                },
                {
                    id: 'compliment_stranger',
                    name: 'Compliment a Stranger',
                    description: 'Give a genuine compliment to someone you don\'t know.',
                    duration: 2,
                    difficulty: 'Medium',
                    points: 15,
                    icon: '😊',
                    instructions: 'Find someone and give them a sincere, appropriate compliment. Make their day better.',
                    category: 'social'
                },
                {
                    id: 'difficult_conversation',
                    name: 'Difficult Conversation',
                    description: 'Have a conversation you\'ve been avoiding. Address the elephant in the room.',
                    duration: 15,
                    difficulty: 'Hard',
                    points: 35,
                    icon: '💬',
                    instructions: 'Identify a conversation you\'ve been putting off. Approach it with honesty and courage.',
                    category: 'social'
                }
            ],
            digital: [
                {
                    id: 'phone_away',
                    name: 'Phone-Free Hour',
                    description: 'Put your phone in another room for one full hour. No checking allowed.',
                    duration: 60,
                    difficulty: 'Medium',
                    points: 20,
                    icon: '📵',
                    instructions: 'Place your phone in a different room. Engage in offline activities for one hour.',
                    category: 'digital'
                },
                {
                    id: 'social_media_detox',
                    name: 'Social Media Detox',
                    description: 'No social media for the entire day. Break the dopamine cycle.',
                    duration: 1440, // 24 hours in minutes
                    difficulty: 'Hard',
                    points: 40,
                    icon: '🚫',
                    instructions: 'Avoid all social media platforms for 24 hours. Delete apps temporarily if needed.',
                    category: 'digital'
                },
                {
                    id: 'notification_silence',
                    name: 'Notification Silence',
                    description: 'Turn off all non-essential notifications for 4 hours.',
                    duration: 240,
                    difficulty: 'Easy',
                    points: 10,
                    icon: '🔕',
                    instructions: 'Disable notifications except for calls and emergencies. Focus on intentional phone use.',
                    category: 'digital'
                }
            ]
        };
    }

    loadTodaysChallenge() {
        const today = new Date();
        const dayOfYear = Math.floor((today - new Date(today.getFullYear(), 0, 0)) / 1000 / 60 / 60 / 24);
        
        // Get all challenges in a flat array
        const allChallenges = Object.values(this.challenges).flat();
        
        // Select challenge based on day of year (ensures same challenge for same day)
        this.currentChallenge = allChallenges[dayOfYear % allChallenges.length];
        
        this.displayChallenge();
    }

    displayChallenge() {
        if (!this.currentChallenge) return;
        
        document.getElementById('challenge-date').textContent = new Date().toLocaleDateString();
        document.getElementById('challenge-icon').textContent = this.currentChallenge.icon;
        document.getElementById('challenge-name').textContent = this.currentChallenge.name;
        document.getElementById('challenge-description').textContent = this.currentChallenge.description;
        document.getElementById('challenge-duration').textContent = `${this.currentChallenge.duration} min`;
        document.getElementById('challenge-difficulty').textContent = this.currentChallenge.difficulty;
        document.getElementById('challenge-points').textContent = this.currentChallenge.points;
        
        // Check if already completed today
        const today = this.getTodayKey();
        const isCompleted = this.userProgress.dailyChallenges[today];
        
        if (isCompleted) {
            const startBtn = document.getElementById('start-challenge');
            startBtn.textContent = '✅ Completed';
            startBtn.disabled = true;
            startBtn.classList.add('completed');
        }
    }

    startChallenge() {
        // Show instructions modal
        const modal = document.getElementById('challenge-modal');
        const instructionContent = document.getElementById('instruction-content');
        
        instructionContent.innerHTML = `
            <div class="challenge-preview">
                <div class="challenge-icon-large">${this.currentChallenge.icon}</div>
                <h3>${this.currentChallenge.name}</h3>
                <p class="challenge-full-description">${this.currentChallenge.description}</p>
                <div class="challenge-instructions">
                    <h4>Instructions:</h4>
                    <p>${this.currentChallenge.instructions}</p>
                </div>
                <div class="challenge-details-modal">
                    <span><strong>Duration:</strong> ${this.currentChallenge.duration} minutes</span>
                    <span><strong>Difficulty:</strong> ${this.currentChallenge.difficulty}</span>
                    <span><strong>Points:</strong> ${this.currentChallenge.points}</span>
                </div>
            </div>
        `;
        
        modal.style.display = 'flex';
    }

    beginChallenge() {
        // Start the timer
        this.timer = this.currentChallenge.duration * 60; // Convert to seconds
        this.startTime = new Date();
        this.isPaused = false;
        
        // Show timer
        document.getElementById('challenge-timer').style.display = 'block';
        document.getElementById('start-challenge').style.display = 'none';
        
        this.updateTimerDisplay();
        this.startTimer();
    }

    startTimer() {
        this.timerInterval = setInterval(() => {
            if (!this.isPaused && this.timer > 0) {
                this.timer--;
                this.updateTimerDisplay();
                
                if (this.timer === 0) {
                    this.timerComplete();
                }
            }
        }, 1000);
    }

    updateTimerDisplay() {
        const minutes = Math.floor(this.timer / 60);
        const seconds = this.timer % 60;
        
        document.getElementById('timer-minutes').textContent = minutes.toString().padStart(2, '0');
        document.getElementById('timer-seconds').textContent = seconds.toString().padStart(2, '0');
    }

    pauseTimer() {
        this.isPaused = !this.isPaused;
        const pauseBtn = document.querySelector('.timer-controls .timer-btn');
        pauseBtn.textContent = this.isPaused ? '▶️' : '⏸️';
    }

    stopTimer() {
        if (confirm('Are you sure you want to stop the challenge?')) {
            this.resetTimer();
        }
    }

    resetTimer() {
        clearInterval(this.timerInterval);
        this.timer = null;
        this.isPaused = false;
        
        document.getElementById('challenge-timer').style.display = 'none';
        document.getElementById('start-challenge').style.display = 'block';
    }

    timerComplete() {
        clearInterval(this.timerInterval);
        this.showCompletionPrompt();
    }

    showCompletionPrompt() {
        // Auto-complete when timer finishes, but allow manual completion too
        setTimeout(() => {
            if (confirm('Timer finished! Did you complete the challenge successfully?')) {
                this.completeChallenge();
            } else {
                this.resetTimer();
            }
        }, 1000);
    }

    completeChallenge() {
        const today = this.getTodayKey();
        const points = this.currentChallenge.points;
        
        // Mark challenge as completed
        this.userProgress.dailyChallenges[today] = {
            challengeId: this.currentChallenge.id,
            completed: true,
            points: points,
            timestamp: new Date().toISOString()
        };
        
        // Update total points
        this.userProgress.totalPoints += points;
        
        // Update streak
        this.updateStreak();
        
        // Save progress
        this.saveProgress();
        
        // Show completion modal
        this.showCompletionModal(points);
        
        // Update UI
        this.displayChallenge();
        this.updateProgress();
        this.updateAchievements();
        
        // Reset timer
        this.resetTimer();
    }

    showCompletionModal(points) {
        const modal = document.getElementById('completion-modal');
        document.getElementById('earned-points').textContent = `+${points}`;
        document.getElementById('new-streak').textContent = `${this.userProgress.currentStreak} days`;
        
        const messages = [
            "Excellent work! You're building mental strength one challenge at a time.",
            "Discipline is the bridge between goals and accomplishment. Well done!",
            "Every challenge completed is a step toward the person you want to become.",
            "You're proving to yourself that you can do hard things. Keep going!",
            "Mental toughness is built through consistent action. You're on the right path."
        ];
        
        document.getElementById('completion-message').textContent = 
            messages[Math.floor(Math.random() * messages.length)];
        
        modal.style.display = 'flex';
    }

    skipChallenge() {
        if (confirm('Are you sure you want to skip today\'s challenge? This will break your streak.')) {
            const today = this.getTodayKey();
            this.userProgress.dailyChallenges[today] = {
                challengeId: this.currentChallenge.id,
                completed: false,
                skipped: true,
                timestamp: new Date().toISOString()
            };
            
            // Reset streak
            this.userProgress.currentStreak = 0;
            
            this.saveProgress();
            this.updateProgress();
            this.updateStreak();
            
            RewireUtils.showNotification('Challenge skipped. Try again tomorrow!', 'warning');
        }
    }

    updateProgress() {
        const stats = this.calculateStats();
        
        document.getElementById('total-points').textContent = stats.totalPoints;
        document.getElementById('completed-challenges').textContent = stats.completedChallenges;
        document.getElementById('success-rate').textContent = `${stats.successRate}%`;
        document.getElementById('longest-streak').textContent = stats.longestStreak;
    }

    calculateStats() {
        const challenges = this.userProgress.dailyChallenges;
        const completed = Object.values(challenges).filter(c => c.completed).length;
        const total = Object.keys(challenges).length;
        const successRate = total > 0 ? Math.round((completed / total) * 100) : 0;
        
        return {
            totalPoints: this.userProgress.totalPoints,
            completedChallenges: completed,
            successRate: successRate,
            longestStreak: this.userProgress.longestStreak || 0
        };
    }

    updateStreak() {
        let currentStreak = 0;
        let longestStreak = 0;
        let tempStreak = 0;
        
        // Calculate current streak (from today backwards)
        let currentDate = new Date();
        while (true) {
            const dateKey = this.formatDate(currentDate);
            const challenge = this.userProgress.dailyChallenges[dateKey];
            
            if (challenge && challenge.completed) {
                currentStreak++;
                currentDate.setDate(currentDate.getDate() - 1);
            } else {
                break;
            }
        }
        
        // Calculate longest streak
        const sortedDates = Object.keys(this.userProgress.dailyChallenges).sort();
        for (const date of sortedDates) {
            const challenge = this.userProgress.dailyChallenges[date];
            if (challenge && challenge.completed) {
                tempStreak++;
                longestStreak = Math.max(longestStreak, tempStreak);
            } else {
                tempStreak = 0;
            }
        }
        
        this.userProgress.currentStreak = currentStreak;
        this.userProgress.longestStreak = longestStreak;
        
        document.getElementById('streak-number').textContent = currentStreak;
        
        this.saveProgress();
    }

    renderCalendar() {
        const calendarGrid = document.getElementById('calendar-grid');
        const today = new Date();
        const startOfWeek = new Date(today);
        startOfWeek.setDate(today.getDate() - today.getDay()); // Start from Sunday
        
        calendarGrid.innerHTML = '';
        
        for (let i = 0; i < 7; i++) {
            const date = new Date(startOfWeek);
            date.setDate(startOfWeek.getDate() + i);
            
            const dateKey = this.formatDate(date);
            const challenge = this.userProgress.dailyChallenges[dateKey];
            const isToday = this.formatDate(date) === this.formatDate(today);
            
            const dayElement = document.createElement('div');
            dayElement.className = 'calendar-day';
            
            if (isToday) dayElement.classList.add('today');
            if (challenge && challenge.completed) dayElement.classList.add('completed');
            if (challenge && challenge.skipped) dayElement.classList.add('skipped');
            
            dayElement.innerHTML = `
                <div class="day-name">${date.toLocaleDateString('en', { weekday: 'short' })}</div>
                <div class="day-number">${date.getDate()}</div>
                <div class="day-status">${challenge && challenge.completed ? '✅' : challenge && challenge.skipped ? '❌' : '⭕'}</div>
            `;
            
            calendarGrid.appendChild(dayElement);
        }
    }

    updateAchievements() {
        const stats = this.calculateStats();
        const achievements = document.querySelectorAll('.achievement-card');
        
        // First Flame
        if (stats.completedChallenges >= 1) {
            achievements[0].classList.remove('locked');
            achievements[0].classList.add('unlocked');
        }
        
        // Week Warrior
        if (this.userProgress.currentStreak >= 7) {
            achievements[1].classList.remove('locked');
            achievements[1].classList.add('unlocked');
        }
        
        // Diamond Mind
        if (this.userProgress.longestStreak >= 30) {
            achievements[2].classList.remove('locked');
            achievements[2].classList.add('unlocked');
        }
        
        // Discipline Master
        if (stats.completedChallenges >= 100) {
            achievements[3].classList.remove('locked');
            achievements[3].classList.add('unlocked');
        }
        
        // Update progress text
        achievements[0].querySelector('.achievement-progress').textContent = `${Math.min(stats.completedChallenges, 1)}/1`;
        achievements[1].querySelector('.achievement-progress').textContent = `${Math.min(this.userProgress.currentStreak, 7)}/7`;
        achievements[2].querySelector('.achievement-progress').textContent = `${Math.min(this.userProgress.longestStreak, 30)}/30`;
        achievements[3].querySelector('.achievement-progress').textContent = `${Math.min(stats.completedChallenges, 100)}/100`;
    }

    loadProgress() {
        return RewireStorage.get('discipline', {
            dailyChallenges: {},
            totalPoints: 0,
            currentStreak: 0,
            longestStreak: 0,
            achievements: []
        });
    }

    saveProgress() {
        RewireStorage.set('discipline', this.userProgress);
    }

    getTodayKey() {
        return this.formatDate(new Date());
    }

    formatDate(date) {
        return date.toISOString().split('T')[0];
    }
}

// Export for global use
window.DisciplineLab = DisciplineLab;
