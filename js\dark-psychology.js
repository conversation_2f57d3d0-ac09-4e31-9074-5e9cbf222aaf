// Dark Psychology Vault Controller
class PsychologyVault {
    constructor() {
        this.userProgress = this.loadProgress();
        this.currentContent = null;
        this.contentDatabase = this.initializeContent();
        this.init();
    }

    init() {
        this.updateProgress();
        this.checkUnlocks();
        this.renderBookmarks();
    }

    loadProgress() {
        return RewireStorage.get('psychology', {
            unlockedContent: ['fundamentals'],
            readArticles: [],
            bookmarkedContent: [],
            progressLevel: 1
        });
    }

    saveProgress() {
        RewireStorage.set('psychology', this.userProgress);
    }

    initializeContent() {
        return {
            'psychology-basics': {
                title: 'Psychology Basics',
                category: 'fundamentals',
                difficulty: 'Beginner',
                readTime: '5 min read',
                content: `
                    <h3>Understanding Human Behavior</h3>
                    <p>Psychology is the scientific study of mind and behavior. To master influence and persuasion, you must first understand the fundamental drivers of human action.</p>
                    
                    <h3>Core Psychological Principles</h3>
                    <ul>
                        <li><strong>Cognitive Biases:</strong> Mental shortcuts that can lead to errors in judgment</li>
                        <li><strong>Social Proof:</strong> People follow the actions of others</li>
                        <li><strong>Authority:</strong> Humans naturally defer to perceived experts</li>
                        <li><strong>Scarcity:</strong> Limited availability increases perceived value</li>
                        <li><strong>Reciprocity:</strong> People feel obligated to return favors</li>
                    </ul>
                    
                    <h3>Practical Applications</h3>
                    <p>Understanding these principles allows you to:</p>
                    <ul>
                        <li>Recognize when others are trying to influence you</li>
                        <li>Communicate more effectively</li>
                        <li>Build stronger relationships</li>
                        <li>Make better decisions</li>
                    </ul>
                    
                    <p><strong>Remember:</strong> Knowledge is power, but with power comes responsibility. Use these insights ethically and for mutual benefit.</p>
                `
            },
            'self-awareness': {
                title: 'Self-Awareness',
                category: 'fundamentals',
                difficulty: 'Beginner',
                readTime: '7 min read',
                content: `
                    <h3>Know Thyself</h3>
                    <p>Before you can understand and influence others, you must first master yourself. Self-awareness is the foundation of all psychological mastery.</p>
                    
                    <h3>Components of Self-Awareness</h3>
                    <ul>
                        <li><strong>Emotional Awareness:</strong> Understanding your emotions and their triggers</li>
                        <li><strong>Cognitive Patterns:</strong> Recognizing your thinking habits and biases</li>
                        <li><strong>Behavioral Tendencies:</strong> Knowing your default responses to situations</li>
                        <li><strong>Values and Motivations:</strong> Understanding what truly drives you</li>
                    </ul>
                    
                    <h3>Building Self-Awareness</h3>
                    <p>Daily practices to increase self-awareness:</p>
                    <ul>
                        <li>Mindfulness meditation (10 minutes daily)</li>
                        <li>Journaling your thoughts and emotions</li>
                        <li>Seeking feedback from trusted friends</li>
                        <li>Regular self-reflection sessions</li>
                        <li>Tracking your emotional patterns</li>
                    </ul>
                    
                    <h3>The Power of Self-Control</h3>
                    <p>When you understand your own psychological patterns, you gain the ability to:</p>
                    <ul>
                        <li>Remain calm under pressure</li>
                        <li>Make rational decisions instead of emotional ones</li>
                        <li>Resist manipulation attempts</li>
                        <li>Project authentic confidence</li>
                    </ul>
                    
                    <p><strong>Exercise:</strong> For the next week, spend 5 minutes each evening reflecting on your emotional state throughout the day. What triggered different emotions? How did you respond?</p>
                `
            },
            'micro-expressions': {
                title: 'Reading Micro-expressions',
                category: 'body-language',
                difficulty: 'Intermediate',
                readTime: '10 min read',
                content: `
                    <h3>The Language of the Face</h3>
                    <p>Micro-expressions are brief, involuntary facial expressions that reveal true emotions, even when someone is trying to conceal them.</p>
                    
                    <h3>The Seven Universal Emotions</h3>
                    <ul>
                        <li><strong>Happiness:</strong> Raised cheeks, crow's feet around eyes</li>
                        <li><strong>Sadness:</strong> Drooping eyelids, downturned mouth corners</li>
                        <li><strong>Anger:</strong> Lowered brow, tightened lips, flared nostrils</li>
                        <li><strong>Fear:</strong> Raised eyebrows, wide eyes, open mouth</li>
                        <li><strong>Surprise:</strong> Raised eyebrows, wide eyes, dropped jaw</li>
                        <li><strong>Disgust:</strong> Wrinkled nose, raised upper lip</li>
                        <li><strong>Contempt:</strong> One-sided mouth raise</li>
                    </ul>
                    
                    <h3>Detection Techniques</h3>
                    <p>Micro-expressions last only 1/25th to 1/5th of a second. To catch them:</p>
                    <ul>
                        <li>Focus on the person's face during emotional moments</li>
                        <li>Look for asymmetry in facial expressions</li>
                        <li>Watch for expressions that don't match the words</li>
                        <li>Pay attention to the timing of expressions</li>
                    </ul>
                    
                    <p><strong>Practice Exercise:</strong> Watch people in conversations (with sound off) and try to identify their emotions based solely on facial expressions.</p>
                `
            }
        };
    }

    updateProgress() {
        const totalContent = 12; // Total number of content pieces
        const unlockedCount = this.userProgress.unlockedContent.length;
        const progressPercentage = (unlockedCount / totalContent) * 100;
        
        document.getElementById('vault-progress').textContent = `${unlockedCount}/${totalContent}`;
        document.getElementById('progress-fill').style.width = `${progressPercentage}%`;
    }

    checkUnlocks() {
        // Get user stats from other modules
        const disciplineData = RewireStorage.get('discipline', {});
        const therapyData = RewireStorage.get('therapy', {});
        
        const completedChallenges = Object.values(disciplineData.dailyChallenges || {}).filter(c => c.completed).length;
        const currentStreak = disciplineData.currentStreak || 0;
        const therapySessions = therapyData.totalSessions || 0;
        const totalPoints = disciplineData.totalPoints || 0;

        // Check Body Language unlock (5 challenges)
        this.checkCategoryUnlock('body-language', completedChallenges >= 5, completedChallenges, 5);
        
        // Check Persuasion unlock (7-day streak)
        this.checkCategoryUnlock('persuasion', currentStreak >= 7, currentStreak, 7);
        
        // Check Emotional Control unlock (10 therapy sessions)
        this.checkCategoryUnlock('emotional-control', therapySessions >= 10, therapySessions, 10);
        
        // Check Advanced unlock (30-day streak + 1000 points)
        const advancedUnlocked = currentStreak >= 30 && totalPoints >= 1000;
        this.checkAdvancedUnlock(advancedUnlocked, currentStreak, totalPoints);
    }

    checkCategoryUnlock(category, isUnlocked, current, required) {
        const section = document.getElementById(`${category}-section`);
        const status = document.getElementById(`${category}-status`);
        const progress = document.getElementById(`${category}-progress`);
        
        if (isUnlocked && !this.userProgress.unlockedContent.includes(category)) {
            this.unlockCategory(category);
        }
        
        if (isUnlocked) {
            section.classList.add('unlocked');
            status.textContent = '✅ Unlocked';
            status.style.color = 'var(--accent-success)';
            
            // Unlock content cards
            section.querySelectorAll('.content-card').forEach(card => {
                card.classList.remove('locked');
                card.classList.add('unlocked');
            });
        } else {
            progress.textContent = `${current}/${required}`;
        }
    }

    checkAdvancedUnlock(isUnlocked, streak, points) {
        const section = document.getElementById('advanced-section');
        const status = document.getElementById('advanced-status');
        const progress = document.getElementById('advanced-progress');
        
        if (isUnlocked && !this.userProgress.unlockedContent.includes('advanced')) {
            this.unlockCategory('advanced');
        }
        
        if (isUnlocked) {
            section.classList.add('unlocked');
            status.textContent = '✅ Unlocked';
            status.style.color = 'var(--accent-success)';
            
            section.querySelectorAll('.content-card').forEach(card => {
                card.classList.remove('locked');
                card.classList.add('unlocked');
            });
        } else {
            progress.textContent = `${streak}/30 days, ${points}/1000 pts`;
        }
    }

    unlockCategory(category) {
        this.userProgress.unlockedContent.push(category);
        this.saveProgress();
        this.updateProgress();
        
        // Show unlock notification
        this.showUnlockNotification(category);
    }

    showUnlockNotification(category) {
        const categoryNames = {
            'body-language': 'Body Language Mastery',
            'persuasion': 'Persuasion & Influence',
            'emotional-control': 'Emotional Mastery',
            'advanced': 'Advanced Tactics'
        };
        
        const notification = document.createElement('div');
        notification.className = 'unlock-notification';
        notification.innerHTML = `
            <div class="notification-content">
                <h3>🎉 New Content Unlocked!</h3>
                <p>${categoryNames[category]} is now available</p>
                <button onclick="this.parentElement.parentElement.remove()">Awesome!</button>
            </div>
        `;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            if (notification.parentElement) {
                notification.remove();
            }
        }, 5000);
    }

    openContent(contentId) {
        const content = this.contentDatabase[contentId];
        if (!content) return;
        
        this.currentContent = contentId;
        
        // Populate modal
        document.getElementById('content-title').textContent = content.title;
        document.getElementById('content-difficulty').textContent = content.difficulty;
        document.getElementById('content-time').textContent = content.readTime;
        document.getElementById('content-category').textContent = content.category;
        document.getElementById('content-text').innerHTML = content.content;
        
        // Update bookmark button
        const isBookmarked = this.userProgress.bookmarkedContent.includes(contentId);
        document.getElementById('bookmark-btn').textContent = isBookmarked ? '🔖' : '📖';
        
        // Show modal
        document.getElementById('content-modal').style.display = 'flex';
    }

    closeContent() {
        document.getElementById('content-modal').style.display = 'none';
        this.currentContent = null;
    }

    toggleBookmark() {
        if (!this.currentContent) return;
        
        const index = this.userProgress.bookmarkedContent.indexOf(this.currentContent);
        if (index > -1) {
            this.userProgress.bookmarkedContent.splice(index, 1);
            document.getElementById('bookmark-btn').textContent = '📖';
        } else {
            this.userProgress.bookmarkedContent.push(this.currentContent);
            document.getElementById('bookmark-btn').textContent = '🔖';
        }
        
        this.saveProgress();
        this.renderBookmarks();
    }

    markAsRead() {
        if (!this.currentContent) return;
        
        if (!this.userProgress.readArticles.includes(this.currentContent)) {
            this.userProgress.readArticles.push(this.currentContent);
            this.saveProgress();
        }
        
        this.closeContent();
        RewireUtils.showNotification('Article marked as read!', 'success');
    }

    renderBookmarks() {
        const bookmarkList = document.getElementById('bookmark-list');
        
        if (this.userProgress.bookmarkedContent.length === 0) {
            bookmarkList.innerHTML = `
                <div class="empty-state">
                    <p>No bookmarks yet. Start reading to save your favorite content!</p>
                </div>
            `;
            return;
        }
        
        bookmarkList.innerHTML = this.userProgress.bookmarkedContent.map(contentId => {
            const content = this.contentDatabase[contentId];
            if (!content) return '';
            
            return `
                <div class="bookmark-item" onclick="openContent('${contentId}')">
                    <h4>${content.title}</h4>
                    <p>${content.category} • ${content.readTime}</p>
                </div>
            `;
        }).join('');
    }
}

// Export for global use
window.PsychologyVault = PsychologyVault;

// Add notification styles
const style = document.createElement('style');
style.textContent = `
.unlock-notification {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: var(--bg-secondary);
    border: 2px solid var(--accent-success);
    border-radius: var(--radius-lg);
    padding: var(--spacing-xl);
    z-index: 10000;
    text-align: center;
    box-shadow: var(--shadow-lg);
    animation: slideIn 0.3s ease;
}

.notification-content h3 {
    color: var(--accent-success);
    margin-bottom: var(--spacing-md);
}

.notification-content p {
    color: var(--text-secondary);
    margin-bottom: var(--spacing-lg);
}

.notification-content button {
    background: var(--accent-success);
    color: white;
    border: none;
    padding: var(--spacing-sm) var(--spacing-lg);
    border-radius: var(--radius-md);
    cursor: pointer;
    font-weight: 500;
}

.bookmark-item {
    background: var(--bg-secondary);
    padding: var(--spacing-lg);
    border-radius: var(--radius-md);
    border: 1px solid var(--bg-tertiary);
    cursor: pointer;
    transition: all 0.3s ease;
}

.bookmark-item:hover {
    border-color: var(--accent-primary);
    transform: translateY(-1px);
}

.bookmark-item h4 {
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
}

.bookmark-item p {
    color: var(--text-secondary);
    font-size: 0.875rem;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translate(-50%, -50%) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1);
    }
}
`;

document.head.appendChild(style);
