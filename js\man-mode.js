// Man Mode Controller
class ManMode {
    constructor() {
        this.userProgress = this.loadProgress();
        this.currentLesson = null;
        this.lessons = this.initializeLessons();
        this.init();
    }

    init() {
        this.updateLevel();
        this.updateMentalStrength();
        this.updateCurrentModule();
        this.updateAchievements();
    }

    loadProgress() {
        return RewireStorage.get('manMode', {
            level: 1,
            completedModules: ['foundation'],
            currentModule: 'emotional-regulation',
            completedLessons: ['emotional-awareness', 'trigger-identification'],
            achievements: ['self-aware'],
            mentalStrengthScore: 150,
            challengesCompleted: []
        });
    }

    saveProgress() {
        RewireStorage.set('manMode', this.userProgress);
    }

    initializeLessons() {
        return {
            'emotional-awareness': {
                title: 'Emotional Awareness',
                module: 'emotional-regulation',
                content: `
                    <h3>Understanding Your Emotional Landscape</h3>
                    <p>The first step to emotional mastery is awareness. Most men go through life reacting to emotions without understanding them.</p>
                    
                    <h3>The Four Core Emotions</h3>
                    <ul>
                        <li><strong>Anger:</strong> Often masks hurt, fear, or frustration</li>
                        <li><strong>Sadness:</strong> Natural response to loss or disappointment</li>
                        <li><strong>Fear:</strong> Protective mechanism, but can become limiting</li>
                        <li><strong>Joy:</strong> Indicates alignment with values and goals</li>
                    </ul>
                    
                    <h3>Daily Practice</h3>
                    <p>For the next week, check in with yourself three times daily:</p>
                    <ul>
                        <li>Morning: How am I feeling right now?</li>
                        <li>Afternoon: What emotions have I experienced today?</li>
                        <li>Evening: What triggered my strongest emotional responses?</li>
                    </ul>
                    
                    <h3>The Masculine Approach</h3>
                    <p>Emotional awareness isn't about becoming soft - it's about becoming strategic. When you understand your emotions, you can use them as information rather than being controlled by them.</p>
                    
                    <p><strong>Remember:</strong> A strong man knows his emotions. A weak man is controlled by them.</p>
                `
            },
            'trigger-identification': {
                title: 'Trigger Identification',
                module: 'emotional-regulation',
                content: `
                    <h3>Know Your Triggers</h3>
                    <p>Every man has emotional triggers - situations, words, or behaviors that instantly provoke a strong reaction. Identifying these is crucial for emotional mastery.</p>
                    
                    <h3>Common Male Triggers</h3>
                    <ul>
                        <li><strong>Disrespect:</strong> Being dismissed, ignored, or belittled</li>
                        <li><strong>Incompetence:</strong> Feeling inadequate or failing at tasks</li>
                        <li><strong>Injustice:</strong> Witnessing unfairness or being treated unfairly</li>
                        <li><strong>Vulnerability:</strong> Being exposed or emotionally open</li>
                        <li><strong>Control:</strong> Losing autonomy or being micromanaged</li>
                    </ul>
                    
                    <h3>The Trigger Map Exercise</h3>
                    <p>Create a personal trigger map:</p>
                    <ul>
                        <li>Identify your top 5 emotional triggers</li>
                        <li>Rate their intensity (1-10)</li>
                        <li>Note your typical response to each</li>
                        <li>Identify the underlying need or fear</li>
                    </ul>
                    
                    <h3>From Reactive to Responsive</h3>
                    <p>Once you know your triggers, you can:</p>
                    <ul>
                        <li>Recognize them in real-time</li>
                        <li>Pause before reacting</li>
                        <li>Choose a strategic response</li>
                        <li>Address the underlying need</li>
                    </ul>
                    
                    <p><strong>Power Principle:</strong> The man who controls his reactions controls his destiny.</p>
                `
            },
            'response-control': {
                title: 'Response Control',
                module: 'emotional-regulation',
                content: `
                    <h3>The Space Between Stimulus and Response</h3>
                    <p>Viktor Frankl said: "Between stimulus and response there is a space. In that space is our power to choose our response. In our response lies our growth and our freedom."</p>
                    
                    <h3>The STOP Technique</h3>
                    <p>When triggered, use this 4-step process:</p>
                    <ul>
                        <li><strong>S - Stop:</strong> Pause all action and speech</li>
                        <li><strong>T - Take a breath:</strong> Deep breath to activate your prefrontal cortex</li>
                        <li><strong>O - Observe:</strong> What am I feeling? What's really happening?</li>
                        <li><strong>P - Proceed:</strong> Choose your response strategically</li>
                    </ul>
                    
                    <h3>Response Options Framework</h3>
                    <p>You always have four response options:</p>
                    <ul>
                        <li><strong>Accept:</strong> Let it go, don't engage</li>
                        <li><strong>Address:</strong> Communicate directly and calmly</li>
                        <li><strong>Adapt:</strong> Change your approach or perspective</li>
                        <li><strong>Avoid:</strong> Remove yourself from the situation</li>
                    </ul>
                    
                    <h3>Building Response Muscle</h3>
                    <p>Practice scenarios:</p>
                    <ul>
                        <li>Someone cuts you off in traffic</li>
                        <li>Your boss criticizes your work unfairly</li>
                        <li>A friend cancels plans last minute</li>
                        <li>You make a mistake in public</li>
                    </ul>
                    
                    <h3>The Masculine Standard</h3>
                    <p>A masculine man responds rather than reacts. He's slow to anger, quick to understand, and strategic in his actions.</p>
                    
                    <p><strong>Challenge:</strong> For the next 48 hours, practice the STOP technique every time you feel triggered.</p>
                `
            }
        };
    }

    updateLevel() {
        document.getElementById('level-number').textContent = this.userProgress.level;
        
        // Update stage indicators
        document.querySelectorAll('.stage').forEach((stage, index) => {
            if (index < this.userProgress.level) {
                stage.classList.add('active');
            } else {
                stage.classList.remove('active');
            }
        });
    }

    updateMentalStrength() {
        const score = this.userProgress.mentalStrengthScore;
        const maxScore = 1000;
        const percentage = (score / maxScore) * 100;
        
        document.getElementById('strength-fill').style.width = `${percentage}%`;
        document.getElementById('strength-score').textContent = `${score}/${maxScore}`;
    }

    updateCurrentModule() {
        const moduleProgress = this.calculateModuleProgress('emotional-regulation');
        document.getElementById('module-progress').textContent = `${moduleProgress.completed}/${moduleProgress.total}`;
        
        // Update lesson statuses
        this.updateLessonStatuses();
    }

    updateLessonStatuses() {
        const lessons = document.querySelectorAll('.lesson-item');
        lessons.forEach((lesson, index) => {
            const lessonId = lesson.getAttribute('onclick')?.match(/'([^']+)'/)?.[1];
            if (!lessonId) return;
            
            if (this.userProgress.completedLessons.includes(lessonId)) {
                lesson.classList.add('completed');
                lesson.classList.remove('current', 'locked');
            } else if (this.isLessonUnlocked(lessonId)) {
                lesson.classList.add('current');
                lesson.classList.remove('completed', 'locked');
            } else {
                lesson.classList.add('locked');
                lesson.classList.remove('completed', 'current');
            }
        });
    }

    isLessonUnlocked(lessonId) {
        // For now, unlock lessons sequentially
        const lessonOrder = ['emotional-awareness', 'trigger-identification', 'response-control', 'emotional-resilience', 'emotional-leadership'];
        const currentIndex = lessonOrder.indexOf(lessonId);
        
        if (currentIndex === 0) return true;
        
        const previousLesson = lessonOrder[currentIndex - 1];
        return this.userProgress.completedLessons.includes(previousLesson);
    }

    calculateModuleProgress(moduleId) {
        const moduleLessons = {
            'emotional-regulation': ['emotional-awareness', 'trigger-identification', 'response-control', 'emotional-resilience', 'emotional-leadership']
        };
        
        const lessons = moduleLessons[moduleId] || [];
        const completed = lessons.filter(lesson => this.userProgress.completedLessons.includes(lesson)).length;
        
        return { completed, total: lessons.length };
    }

    openLesson(lessonId) {
        if (!this.isLessonUnlocked(lessonId)) {
            RewireUtils.showNotification('Complete previous lessons to unlock this one', 'warning');
            return;
        }
        
        const lesson = this.lessons[lessonId];
        if (!lesson) return;
        
        this.currentLesson = lessonId;
        
        // Populate modal
        document.getElementById('lesson-title').textContent = lesson.title;
        document.getElementById('lesson-content').innerHTML = lesson.content;
        
        // Show modal
        document.getElementById('lesson-modal').style.display = 'flex';
    }

    closeLesson() {
        document.getElementById('lesson-modal').style.display = 'none';
        this.currentLesson = null;
    }

    completeLesson() {
        if (!this.currentLesson) return;
        
        if (!this.userProgress.completedLessons.includes(this.currentLesson)) {
            this.userProgress.completedLessons.push(this.currentLesson);
            this.userProgress.mentalStrengthScore += 25;
            
            // Check for module completion
            this.checkModuleCompletion();
            
            this.saveProgress();
            this.updateMentalStrength();
            this.updateCurrentModule();
            this.updateAchievements();
            
            RewireUtils.showNotification('Lesson completed! +25 Mental Strength', 'success');
        }
        
        this.closeLesson();
    }

    checkModuleCompletion() {
        const moduleProgress = this.calculateModuleProgress('emotional-regulation');
        if (moduleProgress.completed === moduleProgress.total && !this.userProgress.completedModules.includes('emotional-regulation')) {
            this.userProgress.completedModules.push('emotional-regulation');
            this.userProgress.level = Math.min(5, this.userProgress.level + 1);
            this.userProgress.mentalStrengthScore += 100;
            
            RewireUtils.showNotification('🎉 Module Completed! Emotional Regulation mastered!', 'success');
        }
    }

    continueCurrentLesson() {
        // Find the next uncompleted lesson
        const lessonOrder = ['emotional-awareness', 'trigger-identification', 'response-control', 'emotional-resilience', 'emotional-leadership'];
        const nextLesson = lessonOrder.find(lesson => !this.userProgress.completedLessons.includes(lesson));
        
        if (nextLesson) {
            this.openLesson(nextLesson);
        } else {
            RewireUtils.showNotification('All lessons in this module are completed!', 'info');
        }
    }

    reviewModule() {
        // Show a summary of completed lessons
        const completedLessons = this.userProgress.completedLessons.filter(lesson => 
            this.lessons[lesson]?.module === 'emotional-regulation'
        );
        
        if (completedLessons.length === 0) {
            RewireUtils.showNotification('No lessons completed yet in this module', 'info');
            return;
        }
        
        const reviewContent = `
            <h3>Module Review: Emotional Regulation</h3>
            <p>You have completed ${completedLessons.length} lessons in this module:</p>
            <ul>
                ${completedLessons.map(lesson => `<li>${this.lessons[lesson].title}</li>`).join('')}
            </ul>
            <p>Keep practicing these concepts daily to build lasting emotional mastery.</p>
        `;
        
        document.getElementById('lesson-title').textContent = 'Module Review';
        document.getElementById('lesson-content').innerHTML = reviewContent;
        document.getElementById('lesson-modal').style.display = 'flex';
    }

    startChallenge(challengeId) {
        const challenges = {
            'cold-exposure': {
                name: 'Cold Exposure',
                description: 'Take a 2-minute cold shower',
                points: 25,
                instructions: 'Start with warm water, then gradually turn it cold. Stay under cold water for at least 2 minutes. Focus on controlling your breathing and staying calm.'
            },
            'direct-communication': {
                name: 'Direct Communication',
                description: 'Have one difficult conversation',
                points: 40,
                instructions: 'Identify a conversation you\'ve been avoiding. Approach the person directly, speak honestly but respectfully, and address the issue head-on.'
            },
            'no-excuses': {
                name: 'No Excuses Day',
                description: 'Complete all planned tasks without excuses',
                points: 35,
                instructions: 'Make a list of tasks for today. Complete every single one without making excuses, procrastinating, or giving up. Push through resistance.'
            }
        };
        
        const challenge = challenges[challengeId];
        if (!challenge) return;
        
        const confirmed = confirm(`Start ${challenge.name}?\n\n${challenge.instructions}\n\nReward: +${challenge.points} Mental Strength points`);
        
        if (confirmed) {
            // In a real app, this would start a timer or tracking system
            setTimeout(() => {
                const completed = confirm(`Did you complete the ${challenge.name} challenge?`);
                if (completed) {
                    this.completeChallenge(challengeId, challenge.points);
                }
            }, 1000);
        }
    }

    completeChallenge(challengeId, points) {
        if (!this.userProgress.challengesCompleted.includes(challengeId)) {
            this.userProgress.challengesCompleted.push(challengeId);
            this.userProgress.mentalStrengthScore += points;
            
            this.saveProgress();
            this.updateMentalStrength();
            this.updateAchievements();
            
            RewireUtils.showNotification(`Challenge completed! +${points} Mental Strength`, 'success');
        }
    }

    updateAchievements() {
        const achievements = document.querySelectorAll('.achievement-card');
        
        // Self-Aware (Foundation module completed)
        if (this.userProgress.completedModules.includes('foundation')) {
            achievements[0].classList.add('unlocked');
            achievements[0].classList.remove('locked');
        }
        
        // Emotional Master (Emotional Regulation module completed)
        const emotionalProgress = this.calculateModuleProgress('emotional-regulation');
        if (this.userProgress.completedModules.includes('emotional-regulation')) {
            achievements[1].classList.add('unlocked');
            achievements[1].classList.remove('locked');
        } else {
            achievements[1].querySelector('.achievement-progress').textContent = `${emotionalProgress.completed}/${emotionalProgress.total} lessons`;
        }
        
        // Rejection Proof (30 rejection challenges)
        const rejectionCount = this.userProgress.challengesCompleted.filter(c => c.includes('rejection')).length;
        achievements[2].querySelector('.achievement-progress').textContent = `${rejectionCount}/30`;
        
        // Alpha Mindset (Level 5)
        achievements[3].querySelector('.achievement-progress').textContent = `Level ${this.userProgress.level}/5`;
        if (this.userProgress.level >= 5) {
            achievements[3].classList.add('unlocked');
            achievements[3].classList.remove('locked');
        }
    }
}

// Export for global use
window.ManMode = ManMode;
