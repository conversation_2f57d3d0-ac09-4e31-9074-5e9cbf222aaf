// Local Storage Management for Rewire Platform
class RewireStorage {
    constructor() {
        this.prefix = 'rewire_';
        this.version = '1.0';
        this.init();
    }

    init() {
        // Check if this is first time user
        if (!this.get('initialized')) {
            this.initializeUserData();
        }
        
        // Check for version updates
        this.checkVersion();
    }

    initializeUserData() {
        const defaultData = {
            initialized: true,
            version: this.version,
            user: {
                id: this.generateUserId(),
                createdAt: new Date().toISOString(),
                isAnonymous: true
            },
            settings: {
                aiMode: 'gentle', // 'gentle' or 'tough'
                voiceEnabled: true,
                anonymousMode: true,
                theme: 'dark',
                notifications: true,
                autoSave: true
            },
            progress: {
                hasVisited: false,
                totalSessions: 0,
                totalPoints: 0,
                currentStreak: 0,
                longestStreak: 0,
                level: 1,
                experience: 0
            },
            therapy: {
                sessions: [],
                totalSessions: 0,
                favoriteTopics: [],
                insights: []
            },
            discipline: {
                dailyChallenges: {},
                completedChallenges: [],
                currentStreak: 0,
                totalPoints: 0,
                achievements: []
            },
            psychology: {
                unlockedContent: ['basics'],
                readArticles: [],
                bookmarkedContent: [],
                progressLevel: 1
            },
            manMode: {
                level: 1,
                completedModules: [],
                currentModule: 'emotional-regulation',
                achievements: [],
                mentalStrengthScore: 0
            },
            mood: {
                dailyMoods: {},
                moodHistory: [],
                insights: [],
                patterns: {}
            },
            silence: {
                totalMinutes: 0,
                favoriteSounds: [],
                sessions: [],
                preferences: {
                    defaultDuration: 10,
                    defaultSound: 'rain'
                }
            }
        };

        // Save all default data
        Object.keys(defaultData).forEach(key => {
            this.set(key, defaultData[key]);
        });
    }

    checkVersion() {
        const currentVersion = this.get('version');
        if (currentVersion !== this.version) {
            this.migrateData(currentVersion, this.version);
        }
    }

    migrateData(fromVersion, toVersion) {
        console.log(`Migrating data from ${fromVersion} to ${toVersion}`);
        // Add migration logic here when needed
        this.set('version', toVersion);
    }

    // Core storage methods
    set(key, value) {
        try {
            localStorage.setItem(this.prefix + key, JSON.stringify(value));
            return true;
        } catch (error) {
            console.error('Storage error:', error);
            return false;
        }
    }

    get(key, defaultValue = null) {
        try {
            const item = localStorage.getItem(this.prefix + key);
            return item ? JSON.parse(item) : defaultValue;
        } catch (error) {
            console.error('Storage retrieval error:', error);
            return defaultValue;
        }
    }

    remove(key) {
        try {
            localStorage.removeItem(this.prefix + key);
            return true;
        } catch (error) {
            console.error('Storage removal error:', error);
            return false;
        }
    }

    clear() {
        try {
            Object.keys(localStorage).forEach(key => {
                if (key.startsWith(this.prefix)) {
                    localStorage.removeItem(key);
                }
            });
            return true;
        } catch (error) {
            console.error('Storage clear error:', error);
            return false;
        }
    }

    // Specialized methods for different data types
    updateProgress(updates) {
        const current = this.get('progress', {});
        const updated = { ...current, ...updates };
        return this.set('progress', updated);
    }

    addTherapySession(session) {
        const therapy = this.get('therapy', { sessions: [] });
        therapy.sessions.push({
            id: this.generateId(),
            timestamp: new Date().toISOString(),
            ...session
        });
        therapy.totalSessions = therapy.sessions.length;
        return this.set('therapy', therapy);
    }

    updateDailyChallenge(date, completed, points = 0) {
        const discipline = this.get('discipline', { dailyChallenges: {} });
        discipline.dailyChallenges[date] = {
            completed,
            points,
            timestamp: new Date().toISOString()
        };
        
        if (completed) {
            discipline.totalPoints = (discipline.totalPoints || 0) + points;
            discipline.currentStreak = this.calculateStreak(discipline.dailyChallenges);
        }
        
        return this.set('discipline', discipline);
    }

    addMoodEntry(date, mood, notes = '') {
        const moodData = this.get('mood', { dailyMoods: {}, moodHistory: [] });
        
        const entry = {
            date,
            mood,
            notes,
            timestamp: new Date().toISOString()
        };
        
        moodData.dailyMoods[date] = entry;
        moodData.moodHistory.push(entry);
        
        return this.set('mood', moodData);
    }

    unlockPsychologyContent(contentId) {
        const psychology = this.get('psychology', { unlockedContent: [] });
        if (!psychology.unlockedContent.includes(contentId)) {
            psychology.unlockedContent.push(contentId);
            return this.set('psychology', psychology);
        }
        return true;
    }

    updateManModeProgress(moduleId, completed = false) {
        const manMode = this.get('manMode', { completedModules: [], level: 1 });
        
        if (completed && !manMode.completedModules.includes(moduleId)) {
            manMode.completedModules.push(moduleId);
            manMode.level = Math.floor(manMode.completedModules.length / 3) + 1;
            manMode.mentalStrengthScore += 10;
        }
        
        return this.set('manMode', manMode);
    }

    addSilenceSession(duration, soundType) {
        const silence = this.get('silence', { sessions: [], totalMinutes: 0 });
        
        const session = {
            id: this.generateId(),
            duration,
            soundType,
            timestamp: new Date().toISOString()
        };
        
        silence.sessions.push(session);
        silence.totalMinutes += duration;
        
        return this.set('silence', silence);
    }

    // Utility methods
    calculateStreak(challenges) {
        let streak = 0;
        let currentDate = new Date();
        
        while (true) {
            const dateKey = this.formatDate(currentDate);
            if (challenges[dateKey] && challenges[dateKey].completed) {
                streak++;
                currentDate.setDate(currentDate.getDate() - 1);
            } else {
                break;
            }
        }
        
        return streak;
    }

    formatDate(date) {
        return date.toISOString().split('T')[0];
    }

    generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }

    generateUserId() {
        return 'user_' + Date.now().toString(36) + '_' + Math.random().toString(36).substr(2, 9);
    }

    // Export/Import functionality
    exportData() {
        const data = {};
        Object.keys(localStorage).forEach(key => {
            if (key.startsWith(this.prefix)) {
                const cleanKey = key.replace(this.prefix, '');
                data[cleanKey] = this.get(cleanKey);
            }
        });
        
        return {
            version: this.version,
            exportDate: new Date().toISOString(),
            data
        };
    }

    importData(importedData) {
        try {
            if (importedData.version && importedData.data) {
                Object.keys(importedData.data).forEach(key => {
                    this.set(key, importedData.data[key]);
                });
                return true;
            }
            return false;
        } catch (error) {
            console.error('Import error:', error);
            return false;
        }
    }

    // Analytics and insights
    getUsageStats() {
        const progress = this.get('progress', {});
        const therapy = this.get('therapy', {});
        const discipline = this.get('discipline', {});
        const mood = this.get('mood', {});
        const silence = this.get('silence', {});
        
        return {
            totalSessions: therapy.totalSessions || 0,
            currentStreak: discipline.currentStreak || 0,
            totalPoints: discipline.totalPoints || 0,
            totalSilenceMinutes: silence.totalMinutes || 0,
            moodEntriesCount: Object.keys(mood.dailyMoods || {}).length,
            daysActive: this.calculateActiveDays(),
            level: progress.level || 1
        };
    }

    calculateActiveDays() {
        const therapy = this.get('therapy', { sessions: [] });
        const discipline = this.get('discipline', { dailyChallenges: {} });
        const mood = this.get('mood', { dailyMoods: {} });
        
        const activeDates = new Set();
        
        // Add therapy session dates
        therapy.sessions.forEach(session => {
            activeDates.add(session.timestamp.split('T')[0]);
        });
        
        // Add challenge dates
        Object.keys(discipline.dailyChallenges).forEach(date => {
            activeDates.add(date);
        });
        
        // Add mood entry dates
        Object.keys(mood.dailyMoods).forEach(date => {
            activeDates.add(date);
        });
        
        return activeDates.size;
    }

    // Backup functionality
    createBackup() {
        const backup = this.exportData();
        const blob = new Blob([JSON.stringify(backup, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        
        const a = document.createElement('a');
        a.href = url;
        a.download = `rewire-backup-${new Date().toISOString().split('T')[0]}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    }
}

// Initialize storage
const storage = new RewireStorage();

// Export for use in other modules
window.RewireStorage = storage;
