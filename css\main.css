/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* Dark Theme Colors */
    --bg-primary: #0a0a0a;
    --bg-secondary: #1a1a1a;
    --bg-tertiary: #2a2a2a;
    --text-primary: #ffffff;
    --text-secondary: #b0b0b0;
    --text-muted: #666666;
    
    /* Accent Colors */
    --accent-primary: #6366f1;
    --accent-secondary: #8b5cf6;
    --accent-success: #10b981;
    --accent-warning: #f59e0b;
    --accent-danger: #ef4444;
    
    /* Gradients */
    --gradient-primary: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
    --gradient-secondary: linear-gradient(135deg, #1e293b 0%, #334155 100%);
    
    /* Spacing */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;
    
    /* Border Radius */
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    
    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    --shadow-glow: 0 0 20px rgba(99, 102, 241, 0.3);
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    background: var(--bg-primary);
    color: var(--text-primary);
    line-height: 1.6;
    overflow-x: hidden;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-md);
}

/* Hero Section */
.hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-2xl) 0;
    position: relative;
}

.hero-content {
    flex: 1;
    max-width: 600px;
}

.hero-title {
    font-size: clamp(2.5rem, 5vw, 4rem);
    font-weight: 700;
    margin-bottom: var(--spacing-lg);
    line-height: 1.1;
}

.gradient-text {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.subtitle {
    display: block;
    color: var(--text-secondary);
    font-weight: 300;
}

.hero-description {
    font-size: 1.25rem;
    color: var(--text-secondary);
    margin-bottom: var(--spacing-2xl);
    line-height: 1.7;
}

.highlight {
    color: var(--accent-primary);
    font-weight: 500;
}

.cta-buttons {
    display: flex;
    gap: var(--spacing-md);
    flex-wrap: wrap;
}

/* Buttons */
.btn {
    padding: var(--spacing-md) var(--spacing-xl);
    border: none;
    border-radius: var(--radius-lg);
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-width: 140px;
}

.btn-primary {
    background: var(--gradient-primary);
    color: white;
    box-shadow: var(--shadow-glow);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 0 30px rgba(99, 102, 241, 0.4);
}

.btn-secondary {
    background: var(--bg-secondary);
    color: var(--text-primary);
    border: 1px solid var(--bg-tertiary);
}

.btn-secondary:hover {
    background: var(--bg-tertiary);
    transform: translateY(-1px);
}

/* Hero Visual */
.hero-visual {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    padding-left: var(--spacing-2xl);
}

.mind-visualization {
    width: 300px;
    height: 300px;
    position: relative;
    border-radius: 50%;
    background: var(--gradient-secondary);
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: var(--shadow-lg);
}

.neural-network {
    width: 200px;
    height: 200px;
    background: radial-gradient(circle, var(--accent-primary) 2px, transparent 2px);
    background-size: 20px 20px;
    border-radius: 50%;
    opacity: 0.6;
    animation: pulse 3s ease-in-out infinite;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); opacity: 0.6; }
    50% { transform: scale(1.05); opacity: 0.8; }
}

/* Features Section */
.features {
    padding: var(--spacing-2xl) 0;
    background: var(--bg-secondary);
    margin-top: var(--spacing-2xl);
    border-radius: var(--radius-xl);
}

.section-title {
    text-align: center;
    font-size: 2.5rem;
    font-weight: 600;
    margin-bottom: var(--spacing-2xl);
    color: var(--text-primary);
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: var(--spacing-xl);
    margin-top: var(--spacing-2xl);
}

.feature-card {
    background: var(--bg-primary);
    padding: var(--spacing-xl);
    border-radius: var(--radius-lg);
    border: 1px solid var(--bg-tertiary);
    transition: all 0.3s ease;
    cursor: pointer;
}

.feature-card:hover {
    transform: translateY(-5px);
    border-color: var(--accent-primary);
    box-shadow: var(--shadow-lg);
}

.feature-icon {
    font-size: 2.5rem;
    margin-bottom: var(--spacing-md);
}

.feature-card h3 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: var(--spacing-md);
    color: var(--text-primary);
}

.feature-card p {
    color: var(--text-secondary);
    margin-bottom: var(--spacing-lg);
    line-height: 1.6;
}

.feature-tags {
    display: flex;
    gap: var(--spacing-sm);
    flex-wrap: wrap;
}

.tag {
    background: var(--bg-tertiary);
    color: var(--text-secondary);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    font-size: 0.875rem;
    font-weight: 500;
}

/* Dashboard */
.dashboard {
    padding: var(--spacing-2xl) 0;
}

.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-lg);
    margin-top: var(--spacing-xl);
}

.dashboard-card {
    background: var(--bg-secondary);
    padding: var(--spacing-xl);
    border-radius: var(--radius-lg);
    border: 1px solid var(--bg-tertiary);
    cursor: pointer;
    transition: all 0.3s ease;
}

.dashboard-card:hover {
    transform: translateY(-3px);
    border-color: var(--accent-primary);
    box-shadow: var(--shadow-md);
}

.card-header {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-md);
}

.card-icon {
    font-size: 1.5rem;
}

.card-header h3 {
    font-size: 1.25rem;
    font-weight: 600;
}

.card-status {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin-top: var(--spacing-md);
    font-size: 0.875rem;
    color: var(--text-secondary);
}

.status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: var(--text-muted);
}

.status-indicator.ready {
    background: var(--accent-success);
    animation: pulse-green 2s ease-in-out infinite;
}

.status-indicator.calm {
    background: var(--accent-primary);
}

@keyframes pulse-green {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* Settings Panel */
.settings-panel {
    position: fixed;
    top: 0;
    right: -400px;
    width: 400px;
    height: 100vh;
    background: var(--bg-secondary);
    border-left: 1px solid var(--bg-tertiary);
    transition: right 0.3s ease;
    z-index: 1000;
    padding: var(--spacing-xl);
}

.settings-panel.open {
    right: 0;
}

.settings-content h3 {
    margin-bottom: var(--spacing-xl);
    font-size: 1.5rem;
}

.setting-group {
    margin-bottom: var(--spacing-lg);
}

.setting-group label {
    display: block;
    margin-bottom: var(--spacing-sm);
    color: var(--text-secondary);
    font-weight: 500;
}

.setting-group select,
.setting-group input[type="checkbox"] {
    width: 100%;
    padding: var(--spacing-sm);
    background: var(--bg-primary);
    border: 1px solid var(--bg-tertiary);
    border-radius: var(--radius-md);
    color: var(--text-primary);
}

/* Bottom Navigation */
.bottom-nav {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: var(--bg-secondary);
    border-top: 1px solid var(--bg-tertiary);
    display: flex;
    justify-content: space-around;
    padding: var(--spacing-sm) 0;
    z-index: 100;
}

.nav-item {
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-sm);
    transition: color 0.3s ease;
}

.nav-item:hover,
.nav-item.active {
    color: var(--accent-primary);
}

.nav-icon {
    font-size: 1.25rem;
}

.nav-label {
    font-size: 0.75rem;
    font-weight: 500;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero {
        flex-direction: column;
        text-align: center;
        padding: var(--spacing-xl) 0;
    }
    
    .hero-visual {
        padding-left: 0;
        margin-top: var(--spacing-xl);
    }
    
    .mind-visualization {
        width: 200px;
        height: 200px;
    }
    
    .neural-network {
        width: 120px;
        height: 120px;
    }
    
    .features-grid,
    .dashboard-grid {
        grid-template-columns: 1fr;
    }
    
    .cta-buttons {
        justify-content: center;
    }
    
    .settings-panel {
        width: 100%;
        right: -100%;
    }
}

/* Utility Classes */
.hidden {
    display: none !important;
}

.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}
