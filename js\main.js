// Main Application Controller
class RewireApp {
    constructor() {
        this.currentPage = 'landing';
        this.userSettings = this.loadSettings();
        this.userProgress = this.loadProgress();
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.updateDashboard();
        this.loadDailyChallenge();
        
        // Check if user is returning
        if (this.userProgress.hasVisited) {
            this.showQuickAccess();
        }
    }

    setupEventListeners() {
        // Feature card clicks
        document.querySelectorAll('.feature-card').forEach(card => {
            card.addEventListener('click', (e) => {
                const feature = e.currentTarget.dataset.feature;
                this.navigateToFeature(feature);
            });
        });

        // Settings panel
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.closeSettings();
            }
        });
    }

    // Navigation Functions
    navigateToFeature(feature) {
        const featureMap = {
            'therapy': 'therapy-room',
            'psychology': 'psychology-vault',
            'discipline': 'discipline-lab',
            'manmode': 'man-mode',
            'mood': 'mood-mirror',
            'silence': 'silence-space'
        };
        
        const page = featureMap[feature];
        if (page) {
            this.navigateTo(page);
        }
    }

    navigateTo(page) {
        // Mark user as having visited
        this.userProgress.hasVisited = true;
        this.saveProgress();
        
        // Navigate to page
        window.location.href = `pages/${page}.html`;
    }

    // Dashboard Functions
    showDashboard() {
        document.querySelector('.hero').style.display = 'none';
        document.querySelector('#features').style.display = 'none';
        document.querySelector('#dashboard').style.display = 'block';
        document.querySelector('#bottom-nav').style.display = 'flex';
        
        this.currentPage = 'dashboard';
        this.updateDashboard();
    }

    updateDashboard() {
        // Update daily challenge
        const challengeElement = document.getElementById('daily-challenge');
        const statusElement = document.getElementById('challenge-status');
        const streakElement = document.getElementById('streak-count');
        
        if (challengeElement) {
            const challenge = this.getDailyChallenge();
            challengeElement.textContent = challenge.name;
            
            // Update challenge status
            const isCompleted = this.userProgress.dailyChallenges[this.getTodayKey()];
            statusElement.className = `status-indicator ${isCompleted ? 'ready' : ''}`;
            
            // Update streak
            const streak = this.calculateStreak();
            streakElement.textContent = `${streak} day streak`;
        }

        // Update vault progress
        const vaultProgress = document.getElementById('vault-progress');
        if (vaultProgress) {
            const unlocked = this.userProgress.unlockedContent.length;
            vaultProgress.textContent = `${unlocked}/12 unlocked`;
        }

        // Update man mode level
        const manLevel = document.getElementById('man-level');
        if (manLevel) {
            manLevel.textContent = `Level ${this.userProgress.manModeLevel}`;
        }

        // Update mood today
        const moodToday = document.getElementById('mood-today');
        if (moodToday) {
            const todayMood = this.userProgress.moods[this.getTodayKey()];
            moodToday.textContent = todayMood ? `Feeling ${todayMood}` : 'How are you feeling?';
        }
    }

    // Challenge System
    loadDailyChallenge() {
        const challenges = [
            { name: "5-Minute Silence", description: "Sit in complete silence for 5 minutes", points: 10 },
            { name: "Dopamine Detox", description: "No social media for 1 hour", points: 15 },
            { name: "Cold Exposure", description: "Take a cold shower", points: 20 },
            { name: "Rejection Practice", description: "Ask for something you expect to be denied", points: 25 },
            { name: "Breath Control", description: "Hold your breath for 30 seconds, 5 times", points: 15 },
            { name: "Digital Minimalism", description: "Use phone for less than 2 hours today", points: 30 },
            { name: "Emotional Awareness", description: "Write down 3 emotions you felt today", points: 10 }
        ];

        const today = new Date().getDay();
        return challenges[today % challenges.length];
    }

    getDailyChallenge() {
        return this.loadDailyChallenge();
    }

    calculateStreak() {
        const challenges = this.userProgress.dailyChallenges;
        let streak = 0;
        let currentDate = new Date();
        
        while (true) {
            const dateKey = this.formatDate(currentDate);
            if (challenges[dateKey]) {
                streak++;
                currentDate.setDate(currentDate.getDate() - 1);
            } else {
                break;
            }
        }
        
        return streak;
    }

    // Settings Functions
    showSettings() {
        document.getElementById('settings-panel').classList.add('open');
    }

    closeSettings() {
        document.getElementById('settings-panel').classList.remove('open');
    }

    loadSettings() {
        const defaultSettings = {
            aiMode: 'gentle',
            voiceEnabled: true,
            anonymousMode: true,
            theme: 'dark'
        };
        
        const saved = localStorage.getItem('rewire_settings');
        return saved ? { ...defaultSettings, ...JSON.parse(saved) } : defaultSettings;
    }

    saveSettings() {
        localStorage.setItem('rewire_settings', JSON.stringify(this.userSettings));
    }

    // Progress Functions
    loadProgress() {
        const defaultProgress = {
            hasVisited: false,
            dailyChallenges: {},
            unlockedContent: ['basics'],
            manModeLevel: 1,
            moods: {},
            therapySessions: 0,
            totalPoints: 0
        };
        
        const saved = localStorage.getItem('rewire_progress');
        return saved ? { ...defaultProgress, ...JSON.parse(saved) } : defaultProgress;
    }

    saveProgress() {
        localStorage.setItem('rewire_progress', JSON.stringify(this.userProgress));
    }

    // Utility Functions
    getTodayKey() {
        return this.formatDate(new Date());
    }

    formatDate(date) {
        return date.toISOString().split('T')[0];
    }

    showQuickAccess() {
        // Show a quick access button for returning users
        const quickAccess = document.createElement('div');
        quickAccess.className = 'quick-access';
        quickAccess.innerHTML = `
            <button class="btn btn-primary" onclick="app.showDashboard()">
                Continue Your Journey
            </button>
        `;
        
        const heroContent = document.querySelector('.hero-content');
        if (heroContent) {
            heroContent.appendChild(quickAccess);
        }
    }

    // Demo Function
    showDemo() {
        alert('Demo: This would show a guided tour of the platform features. Coming soon!');
    }
}

// Global Functions (called from HTML)
function enterPlatform() {
    app.showDashboard();
}

function showDemo() {
    app.showDemo();
}

function navigateTo(page) {
    app.navigateTo(page);
}

function showDashboard() {
    app.showDashboard();
}

function showSettings() {
    app.showSettings();
}

function closeSettings() {
    app.closeSettings();
}

// Initialize App
let app;
document.addEventListener('DOMContentLoaded', () => {
    app = new RewireApp();
});

// Service Worker Registration (for offline functionality)
if ('serviceWorker' in navigator) {
    window.addEventListener('load', () => {
        navigator.serviceWorker.register('/sw.js')
            .then(registration => {
                console.log('SW registered: ', registration);
            })
            .catch(registrationError => {
                console.log('SW registration failed: ', registrationError);
            });
    });
}

// Utility Functions for other modules
window.RewireUtils = {
    formatDate: (date) => date.toISOString().split('T')[0],
    
    saveData: (key, data) => {
        localStorage.setItem(`rewire_${key}`, JSON.stringify(data));
    },
    
    loadData: (key, defaultValue = null) => {
        const saved = localStorage.getItem(`rewire_${key}`);
        return saved ? JSON.parse(saved) : defaultValue;
    },
    
    generateId: () => {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    },
    
    debounce: (func, wait) => {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    },
    
    showNotification: (message, type = 'info') => {
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.textContent = message;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.classList.add('fade-out');
            setTimeout(() => {
                document.body.removeChild(notification);
            }, 300);
        }, 3000);
    }
};

// Global error handler
window.addEventListener('error', (e) => {
    console.error('Global error:', e.error);
    RewireUtils.showNotification('Something went wrong. Please try again.', 'error');
});

// Prevent right-click context menu for a more app-like experience
document.addEventListener('contextmenu', (e) => {
    e.preventDefault();
});

// Add smooth scrolling
document.documentElement.style.scrollBehavior = 'smooth';
