/* Discipline Lab Specific Styles */
.discipline-container {
    min-height: 100vh;
    background: var(--bg-primary);
    padding-bottom: 2rem;
}

/* Header */
.discipline-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-lg) var(--spacing-xl);
    background: var(--bg-secondary);
    border-bottom: 1px solid var(--bg-tertiary);
    position: sticky;
    top: 0;
    z-index: 100;
}

.discipline-title {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    font-size: 1.75rem;
    font-weight: 600;
    color: var(--text-primary);
}

.streak-display {
    text-align: center;
}

.streak-number {
    display: block;
    font-size: 2rem;
    font-weight: 700;
    color: var(--accent-primary);
    line-height: 1;
}

.streak-label {
    font-size: 0.875rem;
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Main Content */
.discipline-main {
    max-width: 1200px;
    margin: 0 auto;
    padding: var(--spacing-xl);
}

.discipline-main section {
    margin-bottom: var(--spacing-2xl);
}

.discipline-main h2 {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-lg);
}

/* Daily Challenge */
.challenge-card {
    background: var(--bg-secondary);
    border-radius: var(--radius-xl);
    padding: var(--spacing-2xl);
    border: 1px solid var(--bg-tertiary);
    position: relative;
    overflow: hidden;
}

.challenge-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-primary);
}

.challenge-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-xl);
}

.challenge-header h2 {
    margin: 0;
    font-size: 1.75rem;
}

.challenge-date {
    color: var(--text-secondary);
    font-size: 0.875rem;
    font-weight: 500;
}

.challenge-content {
    text-align: center;
}

.challenge-icon {
    font-size: 4rem;
    margin-bottom: var(--spacing-lg);
}

.challenge-name {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
}

.challenge-description {
    color: var(--text-secondary);
    font-size: 1.125rem;
    line-height: 1.6;
    margin-bottom: var(--spacing-xl);
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.challenge-details {
    display: flex;
    justify-content: center;
    gap: var(--spacing-xl);
    margin-bottom: var(--spacing-xl);
    flex-wrap: wrap;
}

.detail-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-xs);
}

.detail-label {
    font-size: 0.875rem;
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.detail-value {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-primary);
}

.challenge-actions {
    display: flex;
    gap: var(--spacing-md);
    justify-content: center;
    flex-wrap: wrap;
}

.challenge-btn {
    min-width: 160px;
    font-size: 1.125rem;
    padding: var(--spacing-md) var(--spacing-xl);
}

.challenge-btn.completed {
    background: var(--accent-success);
    cursor: not-allowed;
}

/* Timer */
.challenge-timer {
    margin-top: var(--spacing-xl);
    padding: var(--spacing-xl);
    background: var(--bg-primary);
    border-radius: var(--radius-lg);
    border: 2px solid var(--accent-primary);
}

.timer-display {
    text-align: center;
    margin-bottom: var(--spacing-lg);
}

.timer-minutes,
.timer-seconds {
    font-size: 3rem;
    font-weight: 700;
    color: var(--accent-primary);
    font-family: 'Courier New', monospace;
}

.timer-separator {
    font-size: 3rem;
    color: var(--text-secondary);
    margin: 0 var(--spacing-sm);
}

.timer-controls {
    display: flex;
    justify-content: center;
    gap: var(--spacing-md);
}

.timer-btn {
    width: 50px;
    height: 50px;
    border: none;
    border-radius: 50%;
    background: var(--bg-tertiary);
    color: var(--text-primary);
    cursor: pointer;
    font-size: 1.25rem;
    transition: all 0.3s ease;
}

.timer-btn:hover {
    background: var(--accent-primary);
    transform: scale(1.1);
}

/* Progress Overview */
.progress-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-lg);
}

.progress-card {
    background: var(--bg-secondary);
    padding: var(--spacing-xl);
    border-radius: var(--radius-lg);
    border: 1px solid var(--bg-tertiary);
    text-align: center;
    transition: transform 0.3s ease;
}

.progress-card:hover {
    transform: translateY(-2px);
}

.progress-icon {
    font-size: 2.5rem;
    margin-bottom: var(--spacing-md);
}

.progress-card h3 {
    font-size: 1rem;
    color: var(--text-secondary);
    margin-bottom: var(--spacing-sm);
    font-weight: 500;
}

.progress-value {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-primary);
}

/* Challenge Categories */
.categories-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-lg);
}

.category-card {
    background: var(--bg-secondary);
    padding: var(--spacing-xl);
    border-radius: var(--radius-lg);
    border: 1px solid var(--bg-tertiary);
    cursor: pointer;
    transition: all 0.3s ease;
}

.category-card:hover {
    transform: translateY(-3px);
    border-color: var(--accent-primary);
}

.category-icon {
    font-size: 2.5rem;
    margin-bottom: var(--spacing-md);
}

.category-card h3 {
    font-size: 1.25rem;
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
}

.category-card p {
    color: var(--text-secondary);
    margin-bottom: var(--spacing-lg);
    line-height: 1.5;
}

.category-progress {
    margin-top: var(--spacing-lg);
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: var(--bg-tertiary);
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: var(--spacing-sm);
}

.progress-fill {
    height: 100%;
    background: var(--gradient-primary);
    transition: width 0.3s ease;
}

.progress-text {
    font-size: 0.875rem;
    color: var(--text-secondary);
}

/* Weekly Calendar */
.calendar-grid {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: var(--spacing-sm);
    max-width: 600px;
}

.calendar-day {
    background: var(--bg-secondary);
    padding: var(--spacing-md);
    border-radius: var(--radius-md);
    text-align: center;
    border: 1px solid var(--bg-tertiary);
    transition: all 0.3s ease;
}

.calendar-day.today {
    border-color: var(--accent-primary);
    background: rgba(99, 102, 241, 0.1);
}

.calendar-day.completed {
    background: rgba(16, 185, 129, 0.2);
    border-color: var(--accent-success);
}

.calendar-day.skipped {
    background: rgba(239, 68, 68, 0.2);
    border-color: var(--accent-danger);
}

.day-name {
    font-size: 0.75rem;
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.day-number {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: var(--spacing-xs) 0;
}

.day-status {
    font-size: 1.25rem;
}

/* Achievements */
.achievements-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-lg);
}

.achievement-card {
    background: var(--bg-secondary);
    padding: var(--spacing-xl);
    border-radius: var(--radius-lg);
    border: 1px solid var(--bg-tertiary);
    text-align: center;
    transition: all 0.3s ease;
    position: relative;
}

.achievement-card.locked {
    opacity: 0.5;
    filter: grayscale(100%);
}

.achievement-card.unlocked {
    border-color: var(--accent-success);
    background: rgba(16, 185, 129, 0.1);
}

.achievement-card.unlocked::before {
    content: '✅';
    position: absolute;
    top: var(--spacing-sm);
    right: var(--spacing-sm);
    font-size: 1.25rem;
}

.achievement-icon {
    font-size: 2.5rem;
    margin-bottom: var(--spacing-md);
}

.achievement-card h3 {
    font-size: 1.125rem;
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
}

.achievement-card p {
    color: var(--text-secondary);
    font-size: 0.875rem;
    margin-bottom: var(--spacing-md);
    line-height: 1.5;
}

.achievement-progress {
    font-size: 0.875rem;
    color: var(--accent-primary);
    font-weight: 600;
}

/* Modals */
.challenge-modal,
.completion-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    padding: var(--spacing-lg);
}

.modal-content {
    background: var(--bg-secondary);
    border-radius: var(--radius-lg);
    max-width: 500px;
    width: 100%;
    max-height: 80vh;
    overflow-y: auto;
    border: 1px solid var(--bg-tertiary);
}

.modal-header {
    padding: var(--spacing-xl);
    border-bottom: 1px solid var(--bg-tertiary);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    color: var(--text-primary);
    font-size: 1.25rem;
    margin: 0;
}

.close-btn {
    background: none;
    border: none;
    color: var(--text-secondary);
    font-size: 1.5rem;
    cursor: pointer;
    padding: var(--spacing-xs);
    border-radius: var(--radius-sm);
    transition: color 0.3s ease;
}

.close-btn:hover {
    color: var(--text-primary);
}

.modal-body {
    padding: var(--spacing-xl);
}

.modal-footer {
    padding: var(--spacing-xl);
    border-top: 1px solid var(--bg-tertiary);
    display: flex;
    gap: var(--spacing-md);
    justify-content: flex-end;
}

/* Challenge Preview in Modal */
.challenge-preview {
    text-align: center;
}

.challenge-icon-large {
    font-size: 4rem;
    margin-bottom: var(--spacing-lg);
}

.challenge-full-description {
    color: var(--text-secondary);
    margin-bottom: var(--spacing-lg);
    line-height: 1.6;
}

.challenge-instructions {
    background: var(--bg-primary);
    padding: var(--spacing-lg);
    border-radius: var(--radius-md);
    margin-bottom: var(--spacing-lg);
    text-align: left;
}

.challenge-instructions h4 {
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
    font-size: 1rem;
}

.challenge-instructions p {
    color: var(--text-secondary);
    line-height: 1.6;
}

.challenge-details-modal {
    display: flex;
    justify-content: space-around;
    margin-bottom: var(--spacing-lg);
    font-size: 0.875rem;
    color: var(--text-secondary);
}

/* Completion Modal */
.completion-content {
    text-align: center;
}

.completion-stats {
    display: flex;
    justify-content: space-around;
    margin-bottom: var(--spacing-lg);
    padding: var(--spacing-lg);
    background: var(--bg-primary);
    border-radius: var(--radius-md);
}

.completion-stats .stat {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-xs);
}

.completion-stats .stat-label {
    font-size: 0.875rem;
    color: var(--text-secondary);
}

.completion-stats .stat-value {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--accent-success);
}

.completion-message {
    color: var(--text-secondary);
    line-height: 1.6;
    font-style: italic;
}

/* Responsive Design */
@media (max-width: 768px) {
    .discipline-header {
        padding: var(--spacing-md);
        flex-direction: column;
        gap: var(--spacing-md);
    }
    
    .discipline-title {
        font-size: 1.5rem;
    }
    
    .discipline-main {
        padding: var(--spacing-md);
    }
    
    .challenge-details {
        flex-direction: column;
        gap: var(--spacing-md);
    }
    
    .challenge-actions {
        flex-direction: column;
        align-items: center;
    }
    
    .progress-grid,
    .categories-grid,
    .achievements-grid {
        grid-template-columns: 1fr;
    }
    
    .calendar-grid {
        grid-template-columns: repeat(7, 1fr);
        gap: var(--spacing-xs);
    }
    
    .calendar-day {
        padding: var(--spacing-sm);
    }
    
    .timer-display {
        font-size: 0.8em;
    }
}
