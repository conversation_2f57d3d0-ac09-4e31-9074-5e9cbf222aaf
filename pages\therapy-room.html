<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Therapy Room - Rewire</title>
    <link rel="stylesheet" href="../css/main.css">
    <link rel="stylesheet" href="../css/therapy.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <div class="therapy-container">
        <!-- Header -->
        <header class="therapy-header">
            <button class="back-btn" onclick="goBack()">
                <span class="back-icon">←</span>
                <span>Back</span>
            </button>
            <h1 class="therapy-title">
                <span class="therapy-icon">🧠</span>
                AI Therapy Room
            </h1>
            <div class="therapy-status">
                <span class="status-dot" id="ai-status"></span>
                <span id="ai-status-text">AI Ready</span>
            </div>
        </header>

        <!-- Main Chat Interface -->
        <main class="therapy-main">
            <div class="chat-container" id="chat-container">
                <div class="welcome-message">
                    <div class="ai-avatar">🤖</div>
                    <div class="message-content">
                        <h3>Welcome to your safe space</h3>
                        <p>I'm here to listen without judgment. You can speak freely about anything on your mind. Everything shared here is completely anonymous and private.</p>
                        <div class="quick-starters">
                            <button class="starter-btn" onclick="sendQuickMessage('I\'m feeling overwhelmed today')">
                                I'm feeling overwhelmed
                            </button>
                            <button class="starter-btn" onclick="sendQuickMessage('I need help with my emotions')">
                                Help with emotions
                            </button>
                            <button class="starter-btn" onclick="sendQuickMessage('I want to build mental strength')">
                                Build mental strength
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Input Area -->
            <div class="input-area">
                <div class="input-container">
                    <button class="voice-btn" id="voice-btn" onclick="toggleVoiceInput()">
                        <span class="voice-icon">🎤</span>
                    </button>
                    <textarea 
                        id="message-input" 
                        placeholder="Share what's on your mind... (Press Enter to send, Shift+Enter for new line)"
                        rows="1"
                    ></textarea>
                    <button class="send-btn" id="send-btn" onclick="sendMessage()">
                        <span class="send-icon">→</span>
                    </button>
                </div>
                
                <div class="input-options">
                    <label class="option">
                        <input type="checkbox" id="voice-response" checked>
                        <span>Voice response</span>
                    </label>
                    <label class="option">
                        <input type="checkbox" id="tough-mode">
                        <span>Tough coach mode</span>
                    </label>
                    <button class="clear-btn" onclick="clearChat()">Clear chat</button>
                </div>
            </div>
        </main>

        <!-- Side Panel -->
        <aside class="therapy-sidebar" id="therapy-sidebar">
            <div class="sidebar-content">
                <h3>Session Insights</h3>
                
                <div class="insight-card">
                    <h4>Current Mood</h4>
                    <div class="mood-selector">
                        <button class="mood-btn" data-mood="😔" onclick="setMood('sad')">😔</button>
                        <button class="mood-btn" data-mood="😐" onclick="setMood('neutral')">😐</button>
                        <button class="mood-btn" data-mood="😊" onclick="setMood('happy')">😊</button>
                        <button class="mood-btn" data-mood="😰" onclick="setMood('anxious')">😰</button>
                        <button class="mood-btn" data-mood="😡" onclick="setMood('angry')">😡</button>
                    </div>
                </div>

                <div class="insight-card">
                    <h4>Session Progress</h4>
                    <div class="progress-stats">
                        <div class="stat">
                            <span class="stat-label">Messages</span>
                            <span class="stat-value" id="message-count">0</span>
                        </div>
                        <div class="stat">
                            <span class="stat-label">Duration</span>
                            <span class="stat-value" id="session-duration">0m</span>
                        </div>
                        <div class="stat">
                            <span class="stat-label">Insights</span>
                            <span class="stat-value" id="insight-count">0</span>
                        </div>
                    </div>
                </div>

                <div class="insight-card">
                    <h4>Key Themes</h4>
                    <div class="themes-list" id="themes-list">
                        <span class="theme-tag">Getting started...</span>
                    </div>
                </div>

                <div class="insight-card">
                    <h4>Suggested Actions</h4>
                    <div class="actions-list" id="actions-list">
                        <div class="action-item">
                            <span class="action-icon">💭</span>
                            <span>Continue sharing your thoughts</span>
                        </div>
                    </div>
                </div>
            </div>
        </aside>

        <!-- Floating Controls -->
        <div class="floating-controls">
            <button class="control-btn" onclick="toggleSidebar()" title="Toggle insights">
                <span class="control-icon">📊</span>
            </button>
            <button class="control-btn" onclick="saveSession()" title="Save session">
                <span class="control-icon">💾</span>
            </button>
            <button class="control-btn" onclick="exportChat()" title="Export chat">
                <span class="control-icon">📄</span>
            </button>
        </div>

        <!-- Voice Recording Indicator -->
        <div class="voice-indicator" id="voice-indicator" style="display: none;">
            <div class="voice-animation">
                <div class="voice-wave"></div>
                <div class="voice-wave"></div>
                <div class="voice-wave"></div>
            </div>
            <p>Listening... Speak now</p>
            <button class="stop-voice-btn" onclick="stopVoiceInput()">Stop</button>
        </div>

        <!-- Loading Indicator -->
        <div class="loading-indicator" id="loading-indicator" style="display: none;">
            <div class="loading-dots">
                <div class="dot"></div>
                <div class="dot"></div>
                <div class="dot"></div>
            </div>
            <p>AI is thinking...</p>
        </div>
    </div>

    <!-- Scripts -->
    <script src="../js/storage.js"></script>
    <script src="../js/ai-therapy.js"></script>
    <script>
        // Navigation functions
        function goBack() {
            window.history.back();
        }

        // Initialize therapy session
        document.addEventListener('DOMContentLoaded', () => {
            if (window.TherapyRoom) {
                window.therapyRoom = new TherapyRoom();
            }
        });

        // Auto-resize textarea
        const messageInput = document.getElementById('message-input');
        messageInput.addEventListener('input', function() {
            this.style.height = 'auto';
            this.style.height = Math.min(this.scrollHeight, 120) + 'px';
        });

        // Handle Enter key for sending messages
        messageInput.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                sendMessage();
            }
        });

        // Placeholder functions (will be implemented in ai-therapy.js)
        function sendMessage() {
            if (window.therapyRoom) {
                window.therapyRoom.sendMessage();
            }
        }

        function sendQuickMessage(message) {
            if (window.therapyRoom) {
                window.therapyRoom.sendQuickMessage(message);
            }
        }

        function toggleVoiceInput() {
            if (window.therapyRoom) {
                window.therapyRoom.toggleVoiceInput();
            }
        }

        function stopVoiceInput() {
            if (window.therapyRoom) {
                window.therapyRoom.stopVoiceInput();
            }
        }

        function setMood(mood) {
            if (window.therapyRoom) {
                window.therapyRoom.setMood(mood);
            }
        }

        function clearChat() {
            if (window.therapyRoom) {
                window.therapyRoom.clearChat();
            }
        }

        function toggleSidebar() {
            const sidebar = document.getElementById('therapy-sidebar');
            sidebar.classList.toggle('hidden');
        }

        function saveSession() {
            if (window.therapyRoom) {
                window.therapyRoom.saveSession();
            }
        }

        function exportChat() {
            if (window.therapyRoom) {
                window.therapyRoom.exportChat();
            }
        }
    </script>
</body>
</html>
