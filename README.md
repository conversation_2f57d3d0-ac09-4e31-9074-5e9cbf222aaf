# 🧠 Rewire - AI-Powered Mental Wellness Platform

**Transform broken, distracted minds into focused, emotionally intelligent, and mentally strong individuals.**

Rewire is a comprehensive digital self-talk therapy room and psychological strength-building platform designed especially for young adults and men struggling with internal emotional conflicts, lack of discipline, identity crises, and mental confusion.

## ✨ Features

### 🧠 AI Therapy Room
- Anonymous conversations with an AI therapist
- Voice and text input support
- Gentle therapist or tough coach modes
- Session tracking and insights
- Mood monitoring and analysis

### 🔥 Discipline Lab
- Daily mental and physical challenges
- Streak tracking system
- Progressive difficulty levels
- Achievement unlocks
- Multiple challenge categories:
  - Mental Discipline (silence, meditation, focus)
  - Physical Discipline (cold exposure, breath work)
  - Social Discipline (rejection practice, communication)
  - Digital Discipline (dopamine detox, screen limits)

### 📚 Dark Psychology Vault
- Progressive content unlocking system
- Body language and persuasion techniques
- Emotional control strategies
- Mental influence concepts
- Expert-curated content

### 👑 Man Mode
- Structured masculinity development program
- Emotional regulation training
- Rejection mastery exercises
- Purpose awakening modules
- Mental fortitude building

### 📊 Mood Mirror
- Daily emotion tracking
- Visual progress charts
- Pattern recognition
- Mood insights and recommendations

### 🎧 Silence Space
- Ambient audio environments
- Guided meditation sessions
- Calming background sounds
- Customizable meditation timer

## 🚀 Getting Started

### Prerequisites
- Modern web browser (Chrome, Firefox, Safari, Edge)
- Optional: OpenAI API key for enhanced AI features

### Installation
1. Download or clone this repository
2. Open `index.html` in your web browser
3. No server setup required - runs entirely in the browser!

### API Setup (Optional)
For enhanced AI therapy features:
1. Get an API key from [OpenAI](https://platform.openai.com/api-keys)
2. When prompted in the app, enter your API key
3. The key is stored locally and never shared

**Note:** The platform works without an API key using built-in fallback responses.

## 🎯 How to Use

### First Time Setup
1. Open the platform in your browser
2. Click "Begin Transformation" to access the dashboard
3. Start with the AI Therapy Room to set your baseline mood
4. Try your first Discipline Lab challenge
5. Explore other modules as you progress

### Daily Routine
1. **Morning**: Check in with Mood Mirror
2. **Therapy**: Use AI Therapy Room when needed
3. **Challenge**: Complete your daily Discipline Lab challenge
4. **Learning**: Unlock new Psychology Vault content
5. **Evening**: Relax in the Silence Space

### Progress Tracking
- All data is stored locally in your browser
- Export your progress anytime
- Track streaks, points, and achievements
- Monitor emotional patterns over time

## 🛠️ Technical Details

### Built With
- **Frontend**: HTML5, CSS3, JavaScript (ES6+)
- **Storage**: LocalStorage for data persistence
- **APIs**: 
  - OpenAI API (optional, for enhanced AI features)
  - Web Speech API (for voice features)
  - Web Audio API (for ambient sounds)

### Browser Compatibility
- Chrome 80+ (recommended)
- Firefox 75+
- Safari 13+
- Edge 80+

### File Structure
```
rewire/
├── index.html              # Landing page
├── css/                    # Stylesheets
│   ├── main.css           # Global styles
│   ├── therapy.css        # Therapy room styles
│   └── discipline.css     # Discipline lab styles
├── js/                     # JavaScript modules
│   ├── main.js            # Core functionality
│   ├── storage.js         # Data management
│   ├── ai-therapy.js      # AI therapy logic
│   └── discipline-lab.js  # Challenge system
├── pages/                  # Individual pages
│   ├── therapy-room.html  # AI therapy interface
│   ├── discipline-lab.html # Daily challenges
│   └── [other pages]      # Additional modules
├── config/                 # Configuration
│   └── api-config.js      # API settings
└── assets/                 # Media files
    ├── audio/             # Ambient sounds
    └── images/            # Graphics
```

## 🔒 Privacy & Security

- **Anonymous by Default**: No personal information required
- **Local Storage**: All data stays on your device
- **No Tracking**: No analytics or user tracking
- **Secure**: API keys stored locally, never transmitted
- **Offline Capable**: Core features work without internet

## 🎨 Customization

### Themes
- Dark mode (default)
- Customizable accent colors
- Responsive design for all devices

### AI Personality
- Gentle Therapist: Compassionate, supportive approach
- Tough Coach: Direct, no-nonsense feedback
- Switchable anytime during sessions

### Challenge Difficulty
- Beginner: 1-5 minute challenges
- Intermediate: 5-15 minute challenges
- Advanced: 15+ minute challenges
- Custom: Create your own challenges

## 🤝 Contributing

This is an open-source mental wellness project. Contributions welcome:

1. Fork the repository
2. Create a feature branch
3. Make your improvements
4. Submit a pull request

### Areas for Contribution
- Additional challenge types
- New ambient sounds
- UI/UX improvements
- Mobile app development
- Additional language support

## 📱 Mobile Support

Fully responsive design works on:
- iOS Safari
- Android Chrome
- Progressive Web App (PWA) ready
- Touch-optimized interface

## 🆘 Support

### Troubleshooting
- **Voice not working**: Check browser permissions for microphone
- **Data lost**: Export your data regularly as backup
- **API errors**: Verify your OpenAI API key is valid
- **Performance issues**: Clear browser cache and reload

### Mental Health Resources
This platform is for personal development and is not a replacement for professional mental health care. If you're experiencing severe mental health issues, please contact:

- **Crisis Text Line**: Text HOME to 741741
- **National Suicide Prevention Lifeline**: 988
- **International Association for Suicide Prevention**: https://www.iasp.info/resources/Crisis_Centres/

## 📄 License

MIT License - Feel free to use, modify, and distribute.

## 🙏 Acknowledgments

Inspired by mental health experts and content creators:
- Dr. K (HealthyGamerGG)
- Andrew Huberman
- Tomáš Šebek
- The broader mental wellness community

---

**Remember**: Building mental strength is a journey, not a destination. Be patient with yourself and celebrate small wins. 💪

**Start your transformation today. Your future self will thank you.**
