// Silence Space Controller
class SilenceSpace {
    constructor() {
        this.currentEnvironment = 'rain';
        this.sessionDuration = 5; // minutes
        this.currentAudio = null;
        this.sessionTimer = null;
        this.timeRemaining = 0;
        this.isPaused = false;
        this.isActive = false;
        this.volume = 50;
        this.userProgress = this.loadProgress();
        
        this.init();
    }

    init() {
        this.updateStats();
        this.renderSessionHistory();
        this.setupAudioElements();
    }

    loadProgress() {
        return RewireStorage.get('silence', {
            totalMinutes: 0,
            sessions: [],
            favoriteSounds: [],
            preferences: {
                defaultDuration: 5,
                defaultSound: 'rain',
                defaultVolume: 50
            }
        });
    }

    saveProgress() {
        RewireStorage.set('silence', this.userProgress);
    }

    setupAudioElements() {
        // In a real implementation, you would have actual audio files
        // For now, we'll simulate the audio functionality
        this.audioElements = {
            rain: document.getElementById('rain-audio'),
            forest: document.getElementById('forest-audio'),
            ocean: document.getElementById('ocean-audio'),
            fire: document.getElementById('fire-audio'),
            wind: document.getElementById('wind-audio')
        };

        // Set initial volume
        Object.values(this.audioElements).forEach(audio => {
            if (audio) {
                audio.volume = this.volume / 100;
            }
        });
    }

    selectEnvironment(environment) {
        // Update UI
        document.querySelectorAll('.environment-card').forEach(card => {
            card.classList.remove('active');
        });
        document.querySelector(`[data-environment="${environment}"]`).classList.add('active');
        
        this.currentEnvironment = environment;
        
        // Stop current audio if playing
        if (this.currentAudio && !this.currentAudio.paused) {
            this.currentAudio.pause();
        }
        
        // Update current environment display
        const envData = this.getEnvironmentData(environment);
        document.getElementById('env-name').textContent = envData.name;
        document.querySelector('.env-visual').textContent = envData.icon;
    }

    getEnvironmentData(environment) {
        const environments = {
            rain: { name: 'Gentle Rain', icon: '🌧️' },
            forest: { name: 'Forest Sounds', icon: '🌲' },
            ocean: { name: 'Ocean Waves', icon: '🌊' },
            fire: { name: 'Crackling Fire', icon: '🔥' },
            wind: { name: 'Desert Wind', icon: '💨' },
            silence: { name: 'Pure Silence', icon: '🤫' }
        };
        return environments[environment] || environments.rain;
    }

    setDuration(minutes) {
        // Update UI
        document.querySelectorAll('.duration-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[data-duration="${minutes}"]`)?.classList.add('active');
        
        this.sessionDuration = minutes;
    }

    adjustVolume(value) {
        this.volume = value;
        
        // Update all audio elements
        Object.values(this.audioElements).forEach(audio => {
            if (audio) {
                audio.volume = value / 100;
            }
        });
    }

    startSession() {
        if (this.isActive) return;
        
        this.isActive = true;
        this.isPaused = false;
        this.timeRemaining = this.sessionDuration * 60; // Convert to seconds
        
        // Update UI
        this.showActiveSession();
        this.updateSessionButtons();
        
        // Start audio if not silence
        if (this.currentEnvironment !== 'silence') {
            this.playEnvironmentAudio();
        }
        
        // Start timer
        this.startTimer();
        
        // Update guidance
        this.updateSessionGuidance();
    }

    showActiveSession() {
        document.getElementById('active-session').style.display = 'block';
        
        // Scroll to active session
        document.getElementById('active-session').scrollIntoView({ 
            behavior: 'smooth',
            block: 'center'
        });
    }

    updateSessionButtons() {
        document.getElementById('start-btn').style.display = this.isActive ? 'none' : 'inline-flex';
        document.getElementById('pause-btn').style.display = this.isActive ? 'inline-flex' : 'none';
        document.getElementById('stop-btn').style.display = this.isActive ? 'inline-flex' : 'none';
        
        // Update pause button text
        const pauseBtn = document.getElementById('pause-btn');
        pauseBtn.innerHTML = this.isPaused ? 
            '<span class="btn-icon">▶️</span> Resume' : 
            '<span class="btn-icon">⏸️</span> Pause';
    }

    playEnvironmentAudio() {
        const audio = this.audioElements[this.currentEnvironment];
        if (audio) {
            audio.currentTime = 0;
            audio.play().catch(e => {
                console.log('Audio play failed:', e);
                // Fallback: show visual indication instead
                this.showAudioFallback();
            });
            this.currentAudio = audio;
        } else {
            // Simulate audio with visual feedback
            this.showAudioFallback();
        }
    }

    showAudioFallback() {
        // Since we don't have actual audio files, show visual feedback
        const envVisual = document.querySelector('.env-visual');
        envVisual.style.animation = 'pulse 2s ease-in-out infinite';
    }

    startTimer() {
        this.updateTimerDisplay();
        
        this.sessionTimer = setInterval(() => {
            if (!this.isPaused && this.timeRemaining > 0) {
                this.timeRemaining--;
                this.updateTimerDisplay();
                this.updateTimerProgress();
                
                if (this.timeRemaining === 0) {
                    this.completeSession();
                }
            }
        }, 1000);
    }

    updateTimerDisplay() {
        const minutes = Math.floor(this.timeRemaining / 60);
        const seconds = this.timeRemaining % 60;
        
        document.getElementById('timer-minutes').textContent = minutes.toString().padStart(2, '0');
        document.getElementById('timer-seconds').textContent = seconds.toString().padStart(2, '0');
    }

    updateTimerProgress() {
        const totalSeconds = this.sessionDuration * 60;
        const progress = (totalSeconds - this.timeRemaining) / totalSeconds;
        const circumference = 2 * Math.PI * 45; // radius = 45
        const offset = circumference - (progress * circumference);
        
        document.getElementById('timer-progress').style.strokeDashoffset = offset;
    }

    updateSessionGuidance() {
        const guidance = [
            "Close your eyes and focus on your breathing. Let the sounds wash over you.",
            "Notice any thoughts that arise, then gently return your attention to the present moment.",
            "Feel your body relaxing with each breath. You are safe and at peace.",
            "If your mind wanders, that's normal. Simply bring your attention back to the sounds.",
            "Allow yourself to sink deeper into relaxation with each passing moment."
        ];
        
        let currentGuidance = 0;
        document.getElementById('session-guidance').textContent = guidance[currentGuidance];
        
        // Change guidance every minute
        const guidanceInterval = setInterval(() => {
            if (this.isActive && !this.isPaused) {
                currentGuidance = (currentGuidance + 1) % guidance.length;
                document.getElementById('session-guidance').textContent = guidance[currentGuidance];
            } else {
                clearInterval(guidanceInterval);
            }
        }, 60000);
    }

    pauseSession() {
        this.isPaused = !this.isPaused;
        
        if (this.currentAudio) {
            if (this.isPaused) {
                this.currentAudio.pause();
            } else {
                this.currentAudio.play().catch(e => console.log('Resume audio failed:', e));
            }
        }
        
        this.updateSessionButtons();
    }

    stopSession() {
        if (confirm('Are you sure you want to stop the session?')) {
            this.endSession(false);
        }
    }

    completeSession() {
        this.endSession(true);
        this.showCompletionModal();
    }

    endSession(completed) {
        this.isActive = false;
        this.isPaused = false;
        
        // Stop timer
        if (this.sessionTimer) {
            clearInterval(this.sessionTimer);
            this.sessionTimer = null;
        }
        
        // Stop audio
        if (this.currentAudio) {
            this.currentAudio.pause();
            this.currentAudio.currentTime = 0;
        }
        
        // Reset UI
        document.getElementById('active-session').style.display = 'none';
        this.updateSessionButtons();
        
        // Save session if completed
        if (completed) {
            this.saveSession();
        }
    }

    saveSession() {
        const session = {
            id: Date.now().toString(),
            environment: this.currentEnvironment,
            duration: this.sessionDuration,
            completedAt: new Date().toISOString(),
            mood: null // Will be set in completion modal
        };
        
        this.userProgress.sessions.push(session);
        this.userProgress.totalMinutes += this.sessionDuration;
        
        // Update favorite sounds
        this.updateFavoriteSounds();
        
        this.saveProgress();
        this.updateStats();
        this.renderSessionHistory();
    }

    updateFavoriteSounds() {
        const soundCounts = {};
        this.userProgress.sessions.forEach(session => {
            soundCounts[session.environment] = (soundCounts[session.environment] || 0) + 1;
        });
        
        const favorite = Object.keys(soundCounts).reduce((a, b) => 
            soundCounts[a] > soundCounts[b] ? a : b
        );
        
        this.userProgress.favoriteSounds = [favorite];
    }

    showCompletionModal() {
        const envData = this.getEnvironmentData(this.currentEnvironment);
        
        document.getElementById('completed-duration').textContent = `${this.sessionDuration} minutes`;
        document.getElementById('completed-environment').textContent = envData.name;
        document.getElementById('completion-modal').style.display = 'flex';
    }

    setPostSessionMood(mood) {
        // Update the last session with mood
        if (this.userProgress.sessions.length > 0) {
            this.userProgress.sessions[this.userProgress.sessions.length - 1].mood = mood;
            this.saveProgress();
        }
        
        // Close modal
        document.getElementById('completion-modal').style.display = 'none';
        
        RewireUtils.showNotification(`Session completed! Feeling ${mood}`, 'success');
    }

    updateStats() {
        document.getElementById('total-time').textContent = this.userProgress.totalMinutes;
        document.getElementById('total-minutes').textContent = `${this.userProgress.totalMinutes} minutes`;
        document.getElementById('total-sessions').textContent = `${this.userProgress.sessions.length} sessions`;
        
        // Calculate streak
        const streak = this.calculateStreak();
        document.getElementById('session-streak').textContent = `${streak} days`;
        
        // Update favorite sound
        const favorite = this.userProgress.favoriteSounds[0] || 'Rain';
        const favoriteData = this.getEnvironmentData(favorite);
        document.getElementById('favorite-sound').textContent = favoriteData.name;
    }

    calculateStreak() {
        if (this.userProgress.sessions.length === 0) return 0;
        
        const today = new Date().toDateString();
        const sessionDates = this.userProgress.sessions.map(s => 
            new Date(s.completedAt).toDateString()
        );
        
        const uniqueDates = [...new Set(sessionDates)].sort((a, b) => new Date(b) - new Date(a));
        
        let streak = 0;
        let currentDate = new Date();
        
        for (const dateStr of uniqueDates) {
            if (dateStr === currentDate.toDateString()) {
                streak++;
                currentDate.setDate(currentDate.getDate() - 1);
            } else {
                break;
            }
        }
        
        return streak;
    }

    renderSessionHistory() {
        const sessionsList = document.getElementById('sessions-list');
        
        if (this.userProgress.sessions.length === 0) {
            sessionsList.innerHTML = `
                <div class="empty-state">
                    <p>No sessions yet. Start your first silence session above!</p>
                </div>
            `;
            return;
        }
        
        const recentSessions = this.userProgress.sessions
            .slice(-10)
            .reverse()
            .map(session => {
                const envData = this.getEnvironmentData(session.environment);
                const date = new Date(session.completedAt).toLocaleDateString();
                const time = new Date(session.completedAt).toLocaleTimeString([], { 
                    hour: '2-digit', 
                    minute: '2-digit' 
                });
                
                return `
                    <div class="session-item">
                        <div class="session-env">${envData.icon} ${envData.name}</div>
                        <div class="session-duration">${session.duration} min</div>
                        <div class="session-date">${date} ${time}</div>
                        ${session.mood ? `<div class="session-mood">${this.getMoodEmoji(session.mood)}</div>` : ''}
                    </div>
                `;
            })
            .join('');
        
        sessionsList.innerHTML = recentSessions;
    }

    getMoodEmoji(mood) {
        const moodEmojis = {
            calm: '😌',
            focused: '🎯',
            peaceful: '☮️',
            energized: '⚡'
        };
        return moodEmojis[mood] || '😌';
    }

    startGuidedMeditation(type) {
        const meditations = {
            breathing: {
                name: 'Breathing Focus',
                duration: 5,
                environment: 'silence'
            },
            'body-scan': {
                name: 'Body Scan',
                duration: 10,
                environment: 'silence'
            },
            mindfulness: {
                name: 'Mindfulness',
                duration: 15,
                environment: 'forest'
            },
            strength: {
                name: 'Mental Strength',
                duration: 20,
                environment: 'wind'
            }
        };
        
        const meditation = meditations[type];
        if (!meditation) return;
        
        // Set up the meditation
        this.selectEnvironment(meditation.environment);
        this.setDuration(meditation.duration);
        
        // Start the session
        this.startSession();
        
        // Update guidance for guided meditation
        document.getElementById('session-guidance').textContent = 
            `Starting ${meditation.name} meditation. Follow the guidance and breathe deeply.`;
        
        RewireUtils.showNotification(`Starting ${meditation.name} meditation`, 'info');
    }
}

// Export for global use
window.SilenceSpace = SilenceSpace;

// Add session item styles
const style = document.createElement('style');
style.textContent = `
.session-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-md);
    background: var(--bg-primary);
    border-radius: var(--radius-md);
    margin-bottom: var(--spacing-sm);
    border: 1px solid var(--bg-tertiary);
}

.session-env {
    color: var(--text-primary);
    font-weight: 500;
}

.session-duration {
    color: var(--accent-primary);
    font-weight: 600;
}

.session-date {
    color: var(--text-secondary);
    font-size: 0.875rem;
}

.session-mood {
    font-size: 1.25rem;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); opacity: 1; }
    50% { transform: scale(1.1); opacity: 0.8; }
}

@media (max-width: 768px) {
    .session-item {
        flex-direction: column;
        gap: var(--spacing-xs);
        text-align: center;
    }
}
`;

document.head.appendChild(style);
