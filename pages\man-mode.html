<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Man Mode - Rewire</title>
    <link rel="stylesheet" href="../css/main.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <div class="man-mode-container">
        <header class="man-mode-header">
            <button class="back-btn" onclick="goBack()">
                <span class="back-icon">←</span>
                <span>Back</span>
            </button>
            <h1 class="man-mode-title">
                <span class="man-mode-icon">👑</span>
                Man Mode
            </h1>
            <div class="level-display">
                <span class="level-number" id="level-number">1</span>
                <span class="level-label">Level</span>
            </div>
        </header>

        <main class="man-mode-main">
            <!-- Progress Overview -->
            <section class="progress-overview">
                <div class="transformation-path">
                    <h2>Your Transformation Journey</h2>
                    <div class="path-stages">
                        <div class="stage active" data-stage="1">
                            <div class="stage-icon">🌱</div>
                            <span>Awareness</span>
                        </div>
                        <div class="stage" data-stage="2">
                            <div class="stage-icon">💪</div>
                            <span>Discipline</span>
                        </div>
                        <div class="stage" data-stage="3">
                            <div class="stage-icon">🧠</div>
                            <span>Mastery</span>
                        </div>
                        <div class="stage" data-stage="4">
                            <div class="stage-icon">👑</div>
                            <span>Leadership</span>
                        </div>
                        <div class="stage" data-stage="5">
                            <div class="stage-icon">🔥</div>
                            <span>Transcendence</span>
                        </div>
                    </div>
                </div>
                
                <div class="mental-strength-meter">
                    <h3>Mental Strength Score</h3>
                    <div class="strength-meter">
                        <div class="meter-fill" id="strength-fill" style="width: 15%"></div>
                        <span class="meter-score" id="strength-score">150/1000</span>
                    </div>
                    <p class="meter-description">Build strength through consistent action and emotional mastery</p>
                </div>
            </section>

            <!-- Current Module -->
            <section class="current-module">
                <h2>Current Focus: Emotional Regulation</h2>
                <div class="module-card">
                    <div class="module-header">
                        <div class="module-icon">🎯</div>
                        <div class="module-info">
                            <h3 id="current-module-title">Emotional Regulation</h3>
                            <p id="current-module-description">Master your emotions before they master you</p>
                        </div>
                        <div class="module-progress">
                            <span id="module-progress">2/5</span>
                        </div>
                    </div>
                    
                    <div class="module-content">
                        <div class="lesson-list" id="lesson-list">
                            <div class="lesson-item completed" onclick="openLesson('emotional-awareness')">
                                <div class="lesson-status">✅</div>
                                <div class="lesson-info">
                                    <h4>Emotional Awareness</h4>
                                    <p>Recognize and name your emotions</p>
                                </div>
                                <div class="lesson-duration">10 min</div>
                            </div>
                            
                            <div class="lesson-item completed" onclick="openLesson('trigger-identification')">
                                <div class="lesson-status">✅</div>
                                <div class="lesson-info">
                                    <h4>Trigger Identification</h4>
                                    <p>Understand what sets you off</p>
                                </div>
                                <div class="lesson-duration">15 min</div>
                            </div>
                            
                            <div class="lesson-item current" onclick="openLesson('response-control')">
                                <div class="lesson-status">🎯</div>
                                <div class="lesson-info">
                                    <h4>Response Control</h4>
                                    <p>Choose your response instead of reacting</p>
                                </div>
                                <div class="lesson-duration">20 min</div>
                            </div>
                            
                            <div class="lesson-item locked">
                                <div class="lesson-status">🔒</div>
                                <div class="lesson-info">
                                    <h4>Emotional Resilience</h4>
                                    <p>Bounce back stronger from setbacks</p>
                                </div>
                                <div class="lesson-duration">25 min</div>
                            </div>
                            
                            <div class="lesson-item locked">
                                <div class="lesson-status">🔒</div>
                                <div class="lesson-info">
                                    <h4>Emotional Leadership</h4>
                                    <p>Guide others through emotional intelligence</p>
                                </div>
                                <div class="lesson-duration">30 min</div>
                            </div>
                        </div>
                        
                        <div class="module-actions">
                            <button class="btn btn-primary" onclick="continueLesson()">Continue Lesson</button>
                            <button class="btn btn-secondary" onclick="reviewModule()">Review Module</button>
                        </div>
                    </div>
                </div>
            </section>

            <!-- All Modules -->
            <section class="all-modules">
                <h2>Masculinity Development Modules</h2>
                <div class="modules-grid">
                    
                    <div class="module-overview completed">
                        <div class="module-badge">✅</div>
                        <h3>Foundation</h3>
                        <p>Self-awareness and personal responsibility</p>
                        <div class="module-stats">
                            <span>5/5 lessons</span>
                            <span>100% complete</span>
                        </div>
                    </div>
                    
                    <div class="module-overview current">
                        <div class="module-badge">🎯</div>
                        <h3>Emotional Regulation</h3>
                        <p>Master your emotions and responses</p>
                        <div class="module-stats">
                            <span>2/5 lessons</span>
                            <span>40% complete</span>
                        </div>
                    </div>
                    
                    <div class="module-overview locked">
                        <div class="module-badge">🔒</div>
                        <h3>Rejection Mastery</h3>
                        <p>Build resilience through rejection practice</p>
                        <div class="module-stats">
                            <span>0/6 lessons</span>
                            <span>Locked</span>
                        </div>
                    </div>
                    
                    <div class="module-overview locked">
                        <div class="module-badge">🔒</div>
                        <h3>Purpose & Direction</h3>
                        <p>Find your mission and life purpose</p>
                        <div class="module-stats">
                            <span>0/7 lessons</span>
                            <span>Locked</span>
                        </div>
                    </div>
                    
                    <div class="module-overview locked">
                        <div class="module-badge">🔒</div>
                        <h3>Physical Presence</h3>
                        <p>Command respect through body language</p>
                        <div class="module-stats">
                            <span>0/5 lessons</span>
                            <span>Locked</span>
                        </div>
                    </div>
                    
                    <div class="module-overview locked">
                        <div class="module-badge">🔒</div>
                        <h3>Leadership Mindset</h3>
                        <p>Lead yourself and inspire others</p>
                        <div class="module-stats">
                            <span>0/8 lessons</span>
                            <span>Locked</span>
                        </div>
                    </div>
                    
                </div>
            </section>

            <!-- Daily Challenges -->
            <section class="man-mode-challenges">
                <h2>Masculine Strength Challenges</h2>
                <div class="challenges-list">
                    
                    <div class="challenge-card">
                        <div class="challenge-icon">💪</div>
                        <div class="challenge-content">
                            <h3>Cold Exposure</h3>
                            <p>Take a 2-minute cold shower. Build physical and mental resilience.</p>
                            <div class="challenge-meta">
                                <span class="difficulty">Medium</span>
                                <span class="points">+25 points</span>
                            </div>
                        </div>
                        <button class="challenge-btn" onclick="startChallenge('cold-exposure')">Start</button>
                    </div>
                    
                    <div class="challenge-card">
                        <div class="challenge-icon">🎯</div>
                        <div class="challenge-content">
                            <h3>Direct Communication</h3>
                            <p>Have one difficult conversation you've been avoiding today.</p>
                            <div class="challenge-meta">
                                <span class="difficulty">Hard</span>
                                <span class="points">+40 points</span>
                            </div>
                        </div>
                        <button class="challenge-btn" onclick="startChallenge('direct-communication')">Start</button>
                    </div>
                    
                    <div class="challenge-card">
                        <div class="challenge-icon">🚫</div>
                        <div class="challenge-content">
                            <h3>No Excuses Day</h3>
                            <p>Complete all planned tasks without making excuses or procrastinating.</p>
                            <div class="challenge-meta">
                                <span class="difficulty">Hard</span>
                                <span class="points">+35 points</span>
                            </div>
                        </div>
                        <button class="challenge-btn" onclick="startChallenge('no-excuses')">Start</button>
                    </div>
                    
                </div>
            </section>

            <!-- Achievements -->
            <section class="man-mode-achievements">
                <h2>Masculine Achievements</h2>
                <div class="achievements-grid">
                    
                    <div class="achievement-card unlocked">
                        <div class="achievement-icon">🌱</div>
                        <h3>Self-Aware</h3>
                        <p>Completed Foundation module</p>
                        <div class="achievement-date">Unlocked 3 days ago</div>
                    </div>
                    
                    <div class="achievement-card locked">
                        <div class="achievement-icon">🎯</div>
                        <h3>Emotional Master</h3>
                        <p>Complete Emotional Regulation module</p>
                        <div class="achievement-progress">2/5 lessons</div>
                    </div>
                    
                    <div class="achievement-card locked">
                        <div class="achievement-icon">🔥</div>
                        <h3>Rejection Proof</h3>
                        <p>Complete 30 rejection challenges</p>
                        <div class="achievement-progress">0/30</div>
                    </div>
                    
                    <div class="achievement-card locked">
                        <div class="achievement-icon">👑</div>
                        <h3>Alpha Mindset</h3>
                        <p>Reach Level 5 in Man Mode</p>
                        <div class="achievement-progress">Level 1/5</div>
                    </div>
                    
                </div>
            </section>
        </main>

        <!-- Lesson Modal -->
        <div class="lesson-modal" id="lesson-modal" style="display: none;">
            <div class="modal-overlay" onclick="closeLesson()"></div>
            <div class="modal-content">
                <div class="modal-header">
                    <h3 id="lesson-title">Lesson Title</h3>
                    <button class="close-btn" onclick="closeLesson()">×</button>
                </div>
                <div class="modal-body">
                    <div class="lesson-content" id="lesson-content">
                        <!-- Lesson content will be populated by JavaScript -->
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" onclick="closeLesson()">Close</button>
                    <button class="btn btn-primary" onclick="completeLesson()">Complete Lesson</button>
                </div>
            </div>
        </div>
    </div>

    <script src="../js/storage.js"></script>
    <script src="../js/man-mode.js"></script>
    <script>
        function goBack() {
            window.history.back();
        }

        function openLesson(lessonId) {
            if (window.manMode) {
                window.manMode.openLesson(lessonId);
            }
        }

        function closeLesson() {
            if (window.manMode) {
                window.manMode.closeLesson();
            }
        }

        function completeLesson() {
            if (window.manMode) {
                window.manMode.completeLesson();
            }
        }

        function continueLesson() {
            if (window.manMode) {
                window.manMode.continueCurrentLesson();
            }
        }

        function reviewModule() {
            if (window.manMode) {
                window.manMode.reviewModule();
            }
        }

        function startChallenge(challengeId) {
            if (window.manMode) {
                window.manMode.startChallenge(challengeId);
            }
        }

        document.addEventListener('DOMContentLoaded', () => {
            if (window.ManMode) {
                window.manMode = new ManMode();
            }
        });
    </script>

    <style>
        .man-mode-container {
            min-height: 100vh;
            background: var(--bg-primary);
        }

        .man-mode-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: var(--spacing-lg) var(--spacing-xl);
            background: var(--bg-secondary);
            border-bottom: 1px solid var(--bg-tertiary);
        }

        .man-mode-title {
            display: flex;
            align-items: center;
            gap: var(--spacing-md);
            font-size: 1.75rem;
            font-weight: 600;
            color: var(--text-primary);
        }

        .level-display {
            text-align: center;
        }

        .level-number {
            display: block;
            font-size: 2rem;
            font-weight: 700;
            color: var(--accent-primary);
        }

        .level-label {
            font-size: 0.875rem;
            color: var(--text-secondary);
        }

        .man-mode-main {
            max-width: 1200px;
            margin: 0 auto;
            padding: var(--spacing-xl);
        }

        .man-mode-main section {
            margin-bottom: var(--spacing-2xl);
        }

        .transformation-path {
            background: var(--bg-secondary);
            padding: var(--spacing-xl);
            border-radius: var(--radius-lg);
            margin-bottom: var(--spacing-lg);
        }

        .path-stages {
            display: flex;
            justify-content: space-between;
            margin-top: var(--spacing-lg);
            position: relative;
        }

        .path-stages::before {
            content: '';
            position: absolute;
            top: 25px;
            left: 50px;
            right: 50px;
            height: 2px;
            background: var(--bg-tertiary);
            z-index: 1;
        }

        .stage {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: var(--spacing-sm);
            position: relative;
            z-index: 2;
        }

        .stage-icon {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: var(--bg-tertiary);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            transition: all 0.3s ease;
        }

        .stage.active .stage-icon {
            background: var(--accent-primary);
            color: white;
        }

        .stage span {
            font-size: 0.875rem;
            color: var(--text-secondary);
            font-weight: 500;
        }

        .mental-strength-meter {
            background: var(--bg-secondary);
            padding: var(--spacing-xl);
            border-radius: var(--radius-lg);
            text-align: center;
        }

        .strength-meter {
            position: relative;
            width: 100%;
            height: 20px;
            background: var(--bg-tertiary);
            border-radius: 10px;
            margin: var(--spacing-lg) 0;
            overflow: hidden;
        }

        .meter-fill {
            height: 100%;
            background: var(--gradient-primary);
            transition: width 0.5s ease;
        }

        .meter-score {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-weight: 600;
            color: var(--text-primary);
            font-size: 0.875rem;
        }

        .module-card {
            background: var(--bg-secondary);
            border-radius: var(--radius-lg);
            padding: var(--spacing-xl);
            border: 1px solid var(--bg-tertiary);
        }

        .module-header {
            display: flex;
            align-items: center;
            gap: var(--spacing-lg);
            margin-bottom: var(--spacing-xl);
        }

        .module-icon {
            font-size: 3rem;
            flex-shrink: 0;
        }

        .module-info {
            flex: 1;
        }

        .module-info h3 {
            color: var(--text-primary);
            font-size: 1.5rem;
            margin-bottom: var(--spacing-sm);
        }

        .module-info p {
            color: var(--text-secondary);
        }

        .module-progress {
            background: var(--bg-primary);
            padding: var(--spacing-sm) var(--spacing-md);
            border-radius: var(--radius-md);
            font-weight: 600;
            color: var(--accent-primary);
        }

        .lesson-list {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-xl);
        }

        .lesson-item {
            display: flex;
            align-items: center;
            gap: var(--spacing-lg);
            padding: var(--spacing-lg);
            background: var(--bg-primary);
            border-radius: var(--radius-md);
            border: 1px solid var(--bg-tertiary);
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .lesson-item:hover:not(.locked) {
            border-color: var(--accent-primary);
            transform: translateY(-1px);
        }

        .lesson-item.locked {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .lesson-item.current {
            border-color: var(--accent-primary);
            background: rgba(99, 102, 241, 0.1);
        }

        .lesson-status {
            font-size: 1.25rem;
            width: 30px;
            text-align: center;
        }

        .lesson-info {
            flex: 1;
        }

        .lesson-info h4 {
            color: var(--text-primary);
            margin-bottom: var(--spacing-xs);
        }

        .lesson-info p {
            color: var(--text-secondary);
            font-size: 0.875rem;
        }

        .lesson-duration {
            color: var(--text-muted);
            font-size: 0.875rem;
        }

        .module-actions {
            display: flex;
            gap: var(--spacing-md);
            justify-content: center;
        }

        .modules-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: var(--spacing-lg);
        }

        .module-overview {
            background: var(--bg-secondary);
            padding: var(--spacing-xl);
            border-radius: var(--radius-lg);
            border: 1px solid var(--bg-tertiary);
            position: relative;
        }

        .module-overview.completed {
            border-color: var(--accent-success);
        }

        .module-overview.current {
            border-color: var(--accent-primary);
        }

        .module-overview.locked {
            opacity: 0.6;
        }

        .module-badge {
            position: absolute;
            top: var(--spacing-md);
            right: var(--spacing-md);
            font-size: 1.25rem;
        }

        .module-overview h3 {
            color: var(--text-primary);
            margin-bottom: var(--spacing-sm);
        }

        .module-overview p {
            color: var(--text-secondary);
            margin-bottom: var(--spacing-lg);
            line-height: 1.5;
        }

        .module-stats {
            display: flex;
            justify-content: space-between;
            font-size: 0.875rem;
            color: var(--text-muted);
        }

        .challenges-list {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-lg);
        }

        .challenge-card {
            background: var(--bg-secondary);
            padding: var(--spacing-xl);
            border-radius: var(--radius-lg);
            border: 1px solid var(--bg-tertiary);
            display: flex;
            align-items: center;
            gap: var(--spacing-lg);
        }

        .challenge-icon {
            font-size: 2.5rem;
            flex-shrink: 0;
        }

        .challenge-content {
            flex: 1;
        }

        .challenge-content h3 {
            color: var(--text-primary);
            margin-bottom: var(--spacing-sm);
        }

        .challenge-content p {
            color: var(--text-secondary);
            margin-bottom: var(--spacing-md);
            line-height: 1.5;
        }

        .challenge-meta {
            display: flex;
            gap: var(--spacing-md);
            font-size: 0.875rem;
        }

        .difficulty {
            background: var(--bg-tertiary);
            padding: var(--spacing-xs) var(--spacing-sm);
            border-radius: var(--radius-sm);
            color: var(--text-secondary);
        }

        .points {
            color: var(--accent-success);
            font-weight: 600;
        }

        .challenge-btn {
            background: var(--accent-primary);
            color: white;
            border: none;
            padding: var(--spacing-md) var(--spacing-lg);
            border-radius: var(--radius-md);
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .challenge-btn:hover {
            background: var(--accent-secondary);
            transform: translateY(-1px);
        }

        .achievements-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: var(--spacing-lg);
        }

        .achievement-card {
            background: var(--bg-secondary);
            padding: var(--spacing-xl);
            border-radius: var(--radius-lg);
            border: 1px solid var(--bg-tertiary);
            text-align: center;
        }

        .achievement-card.unlocked {
            border-color: var(--accent-success);
            background: rgba(16, 185, 129, 0.1);
        }

        .achievement-card.locked {
            opacity: 0.6;
        }

        .achievement-icon {
            font-size: 2.5rem;
            margin-bottom: var(--spacing-md);
        }

        .achievement-card h3 {
            color: var(--text-primary);
            margin-bottom: var(--spacing-sm);
        }

        .achievement-card p {
            color: var(--text-secondary);
            margin-bottom: var(--spacing-md);
            line-height: 1.5;
        }

        .achievement-date {
            color: var(--accent-success);
            font-size: 0.875rem;
            font-weight: 500;
        }

        .achievement-progress {
            color: var(--text-muted);
            font-size: 0.875rem;
        }

        /* Modal Styles */
        .lesson-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            z-index: 1000;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: var(--spacing-lg);
        }

        .modal-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.8);
        }

        .modal-content {
            background: var(--bg-secondary);
            border-radius: var(--radius-lg);
            max-width: 800px;
            width: 100%;
            max-height: 90vh;
            overflow-y: auto;
            position: relative;
            z-index: 1001;
        }

        .modal-header {
            padding: var(--spacing-xl);
            border-bottom: 1px solid var(--bg-tertiary);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-body {
            padding: var(--spacing-xl);
        }

        .lesson-content {
            color: var(--text-primary);
            line-height: 1.7;
        }

        .lesson-content h3 {
            color: var(--text-primary);
            margin: var(--spacing-lg) 0 var(--spacing-md) 0;
        }

        .lesson-content p {
            margin-bottom: var(--spacing-md);
        }

        .lesson-content ul {
            margin-bottom: var(--spacing-md);
            padding-left: var(--spacing-lg);
        }

        .modal-footer {
            padding: var(--spacing-xl);
            border-top: 1px solid var(--bg-tertiary);
            display: flex;
            gap: var(--spacing-md);
            justify-content: flex-end;
        }

        @media (max-width: 768px) {
            .man-mode-header {
                flex-direction: column;
                gap: var(--spacing-md);
            }

            .path-stages {
                flex-direction: column;
                gap: var(--spacing-lg);
            }

            .path-stages::before {
                display: none;
            }

            .module-header {
                flex-direction: column;
                text-align: center;
            }

            .lesson-item {
                flex-direction: column;
                text-align: center;
            }

            .challenge-card {
                flex-direction: column;
                text-align: center;
            }

            .modules-grid,
            .achievements-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</body>
</html>
